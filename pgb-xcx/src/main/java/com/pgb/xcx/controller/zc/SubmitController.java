package com.pgb.xcx.controller.zc;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.global.exception.BaseException;
import com.pgb.common.oss.service.OssService;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.db.*;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.domain.common.TodayNum;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.zc.answer.PgZcAnswer;
import com.pgb.service.domain.zc.answer.batch.PgZcAnswerBatch;
import com.pgb.service.domain.zc.homework.PgZcHomework;
import com.pgb.service.domain.zc.question.PgZcQuestion;
import com.pgb.service.domain.zc.question.chinese.ZcSubmitForm;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/4/21 16:26
 */
@Tag(name = "用户端/字词/提交")
@RestController("UserZcSubmitController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/zc/submit")
@RequiredArgsConstructor
@Slf4j
public class SubmitController {

    private final PgZcAnswerService pgZcAnswerService;

    private final PgZcAnswerBatchService pgZcAnswerBatchService;

    private final OssService ossService;

    private final PgZcQuestionService pgZcQuestionService;

    private final PgClassesService pgClassesService;

    private final PgZcHomeworkService pgZcHomeworkService;

    private final PgAnswerCostService pgAnswerCostService;


    @Operation(summary = "重新批改字词作业")
    @PostMapping("reCorrectZc/{zcAnswerId}")
//    @SaCheckLogin
    public BaseResult<Boolean> reCorrect(@PathVariable Long zcAnswerId) {

//        long userId = StpUtil.getLoginIdAsLong();
//
//        // 获取今日批改次数
//        TodayNum submitNum = pgAnswerCostService.getTodaySubmitNum(userId);
//        if (submitNum.getRemainNum() <= 0) {
//            return BaseResult.error("今日批改次数已达上限");
//        }

        PgZcAnswer zcAnswer = pgZcAnswerService.getById(zcAnswerId);

        if (ObjectUtil.isNull(zcAnswer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        try {
            pgZcAnswerService.reCorrect(zcAnswer, false);
        } catch (BaseException e) {
            return BaseResult.error(e.getMsg());
        }
        return BaseResult.success(true);
    }

    @Operation(summary = "重新批改字词超时题目")
    @PostMapping("reCorrectTime/{key}")
    public BaseResult<Integer> reCorrectTime(@PathVariable String key){

        if (ObjectUtil.isNull(key)){
            return BaseResult.error("参数为空");
        }

        if (!"reCorrectTime".equals(key)){
            return BaseResult.error("参数错误");
        }

        return BaseResult.success(
                pgZcAnswerService.queryToCorrect()
        );
    }

    @Operation(summary = "重新批改全部字词作业")
    @PostMapping("reCorrect/{key}")
    public BaseResult<Boolean> reCorrect(@PathVariable String key){

        if (ObjectUtil.isNull(key)){
            return BaseResult.error("参数为空");
        }
        if (!"pgb_reCorrect".equals(key)){
            return BaseResult.error("参数错误");
        }

        // 扫描当前需要批改的字词题目列表
        List<PgZcAnswer> list = pgZcAnswerService.list(new LambdaUpdateWrapper<PgZcAnswer>()
                .eq(PgZcAnswer::getStatus, CorrectStatusEnum.Uploaded));

        log.info("【字词待批改扫描】待批改题目数量:{}个", list.size());

        list.forEach(answer -> {

            // 加入批改队列
            if (!QueueUtils.containsQueueObject(GlobQueueConstants.PGB_XCX_ZC_CORRECT_QUEUE.name(), answer.getId())){
                QueueUtils.addQueueObject(GlobQueueConstants.PGB_XCX_ZC_CORRECT_QUEUE.name(), answer.getId());
            } else {
                log.info("当前字词题目已加入批改队列：{}", answer.getId());
            }
        });
        return BaseResult.success(true);
    }


    @Data
    public static class ZcHomeworkForm {

        @Schema(title = "班级id")
        private Long classId;

        @Schema(title = "字词题目id")
        private Long zcQuestionId;

    }

    @Operation(summary = "获取并创建字词作业", description = "根据班级id和字词题目id")
    @PostMapping("getOrCreateZcHomework")
    @SaCheckLogin
    public BaseResult<Long> getOrCreateZcHomework(@RequestBody ZcHomeworkForm form) {

        // 获取题目
        PgZcQuestion zcQuestion = pgZcQuestionService.getById(form.getZcQuestionId());
        if (ObjectUtil.isNull(zcQuestion)) {
            return BaseResult.error(GlobalCode.Item_Null, "题目不存在");
        }

        // 获取班级
        PgClasses pgClasses = pgClassesService.getById(form.getClassId());
        if (ObjectUtil.isNull(pgClasses)) {
            return BaseResult.error(GlobalCode.Item_Null, "班级不存在");
        }

        PgZcHomework zcHomework = pgZcHomeworkService.getOne(new LambdaQueryWrapper<PgZcHomework>()
                .eq(PgZcHomework::getClassId, form.getClassId())
                .eq(PgZcHomework::getZcQuestionId, form.getZcQuestionId())
                .last("LIMIT 1")
        );

        if (ObjectUtil.isNull(zcHomework)) {
            // 新增作业
            zcHomework = new PgZcHomework();
            zcHomework.setUserId(StpUtil.getLoginIdAsLong());
            zcHomework.setName(zcQuestion.getName());
            zcHomework.setQuestionInfo(zcQuestion);
            zcHomework.setZcQuestionId(form.getZcQuestionId());
            zcHomework.setClassId(form.getClassId());
            zcHomework.setCreateTime(new Date());

            pgZcHomeworkService.save(zcHomework);
        }

        return BaseResult.success(zcHomework.getId());
    }

    @Data
    public static class BatchSubmitZcForm {

        @Schema(title = "上传的字词")
        private List<ZcSubmitForm> zcList;

        @Schema(title = "所属字词题目id")
        private Long zcQuestionId;

        @Schema(title = "题目类型")
        private ZcQuestionTypeEnum questionType;

    }

    @Operation(summary = "【自由上传】提交字词作业", description = "自由上传批改使用")
    @PostMapping("zcSubmit")
    @SaCheckLogin
    public BaseResult<Boolean> submit(@RequestBody BatchSubmitZcForm form) {

        long userId = StpUtil.getLoginIdAsLong();
//        long userId = 1810610447493398529L;

        try {
            // 新增默写提交-批次记录
            PgZcAnswerBatch batch = new PgZcAnswerBatch();
            batch.setCreateTime(new Date());
            batch.setUserId(userId);
            if (ObjectUtil.isNotNull(form.getZcQuestionId())) {
                batch.setZcQuestionId(form.getZcQuestionId());
            }
            pgZcAnswerBatchService.save(batch);

            // 遍历上传数据
            for (ZcSubmitForm zcSubmitForm : form.getZcList()) {

                // 判断是否有效
                if (CollUtil.isEmpty(zcSubmitForm.getUserImgAnswerList())) {
                    return BaseResult.error("请上传图片");
                }

                // 处理图片 重命名
                for (FilePaperImg img : zcSubmitForm.getUserImgAnswerList()) {
                    // 源
                    String sourceKey = URLUtil.getPath(img.getImgUrl());

                    if (sourceKey.startsWith("/tmp/")) {

                        // 将 temp 替换为 zw
                        String destinationKey = StrUtil.replace(sourceKey, "/tmp/", "/zc/");
                        ossService.renameFile(sourceKey, destinationKey);
                        img.setImgUrl(
                                img.getImgUrl().replaceAll("/tmp/", "/zc/")
                        );
                    }
                }
                // 加入提交
                PgZcAnswer zcAnswer = new PgZcAnswer();
                zcAnswer.setUserId(userId);
                zcAnswer.setDeleted(false);
                zcAnswer.setBatchId(batch.getId());
                zcAnswer.setStatus(CorrectStatusEnum.Uploaded);
                zcAnswer.setCreateTime(new Date());
                zcAnswer.setAiTokens(0L);
                // 题目类型
                zcAnswer.setQuesType(form.getQuestionType());

                // 备注名称
                if (StrUtil.isNotBlank(zcSubmitForm.getName())) {
                    zcAnswer.setName(zcSubmitForm.getName());
                }

                // 关联题目
                PgZcQuestion pgZcQuestion;

                // 关联题目
                if (ObjectUtil.isNotNull(form.getZcQuestionId())) {

                    pgZcQuestion = pgZcQuestionService.getById(form.getZcQuestionId());
                    if (ObjectUtil.isNull(pgZcQuestion)) {
                        return BaseResult.error("关联的题目不存在");
                    }

                    // 设置分数
                    if (ObjectUtil.isNotNull(pgZcQuestion.getScore())) {
                        zcSubmitForm.setScore(Double.valueOf(pgZcQuestion.getScore()));
                    }

                    zcAnswer.setZcQuestionId(form.getZcQuestionId());

                    // 关联的题目json
                    // 构建json内容并保存为临时文件
                    String questionJsonStr = JSONUtil.toJsonStr(pgZcQuestion);

                    String md5 = DigestUtil.md5Hex(questionJsonStr);
                    String path = StrUtil.format(
                            "zc/question/{}/{}/{}.json", userId, form.getZcQuestionId(), md5
                    );

                    // 判断是否存在，如果不存在，则提交
                    if (!ossService.isExist(path)) {
                        File tempFile = FileUtil.createTempFile(".json", true);
                        FileUtil.writeUtf8String(questionJsonStr, tempFile);
                        String questionJsonUrl = ossService.putFile(path, tempFile);
                        FileUtil.del(tempFile);
                        zcSubmitForm.setQuestionJsonUrl(questionJsonUrl);
                    }
                    // 存在，则直接赋值url
                    else {
                        zcSubmitForm.setQuestionJsonUrl(
                                ossService.getCdnUrl(path)
                        );
                    }
                }
                // 用户上传数据
                zcAnswer.setAnswer(zcSubmitForm);

                pgZcAnswerService.save(zcAnswer);

                // 【字词批改】新增消耗情况
                pgAnswerCostService.addAnswerCost(userId, zcAnswer.getId(), 1);

                // 加入默写批改队列
                QueueUtils.addQueueObjectInTransaction(
                        GlobQueueConstants.PGB_XCX_ZC_CORRECT_QUEUE.name(), zcAnswer.getId()
                );
            }

        } catch (BaseException e) {
            // 自定义异常
            return BaseResult.error(e.getMsg());
        }

        return BaseResult.success(true);
    }


    @Operation(summary = "【班级上传】提交字词作业", description = "班级上传批改使用")
    @PostMapping("zcSubmitByHomework")
    @SaCheckLogin
    public BaseResult<Boolean> submitByHomework(@RequestBody ZcSubmitForm form) {

        if (ObjectUtil.isNotNull(form.getZcHomeworkId())) {
            if (ObjectUtil.isNull(form.getStudentId())) {
                return BaseResult.error("学生不存在，请返回重试");
            }
        }

        // 获取图片
        List<FilePaperImg> imgList = form.getUserImgAnswerList();
        // 判断是否有效
        if (CollUtil.isEmpty(imgList)) {
            return BaseResult.error("请上传图片");
        }
        try {
            // 判断是否已经提交过
            if (ObjectUtil.isNotNull(form.getZcHomeworkId()) && ObjectUtil.isNotNull(form.getStudentId())) {
                if (pgZcAnswerService.exists(new LambdaQueryWrapper<PgZcAnswer>()
                        .eq(PgZcAnswer::getZcHomeworkId, form.getZcHomeworkId())
                        .eq(PgZcAnswer::getStudentId, form.getStudentId())
                        .eq(PgZcAnswer::getDeleted, false)
                )) {
                    return BaseResult.error("当前学生已提交，请勿重复提交");
                }
            }

            // 处理图片 重命名
            for (FilePaperImg img : imgList) {
                // 源
                String sourceKey = URLUtil.getPath(img.getImgUrl());

                if (sourceKey.contains("/tmp/")) {

                    // 将 temp 替换为 zw
                    String destinationKey = StrUtil.replace(sourceKey, "/tmp/", "/zc/");
                    ossService.renameFile(sourceKey, destinationKey);
                    img.setImgUrl(
                            img.getImgUrl().replaceAll("/tmp/", "/zc/")
                    );
                }
            }

            PgZcAnswer zcAnswer = pgZcAnswerService.submitZcHomeworkAnswer(form, StpUtil.getLoginIdAsLong());

            // 【字词批改】新增消耗情况
            pgAnswerCostService.addAnswerCost(StpUtil.getLoginIdAsLong(), zcAnswer.getId(), 1);

            // 加入默写批改队列
            QueueUtils.addQueueObjectInTransaction(
                    GlobQueueConstants.PGB_XCX_ZC_CORRECT_QUEUE.name(), zcAnswer.getId()
            );

        } catch (BaseException e) {
            // 自定义异常
            return BaseResult.error(e.getMsg());
        }

        return BaseResult.success(true);
    }

}
