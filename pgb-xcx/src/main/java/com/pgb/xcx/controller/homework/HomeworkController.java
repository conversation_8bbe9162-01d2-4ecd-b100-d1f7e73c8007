package com.pgb.xcx.controller.homework;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.mybatis.global.PageQuery;
import com.pgb.common.redis.QueueUtils;
import com.pgb.service.db.*;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.answer.PgAnswerVO;
import com.pgb.service.domain.answer.model.PgAnswerModel;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.domain.export.PgExportRecord;
import com.pgb.service.domain.homework.*;
import com.pgb.service.domain.query.HomeworkQuery;
import com.pgb.service.domain.question.PgQuestion;
import com.pgb.service.domain.question.zwEssay.ZwEssayQuestion;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.student.PgStudentVO;
import com.pgb.service.domain.homework.PgHomeworkReportVO;
import com.pgb.service.enums.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2024/12/10 19:35
 */
@Tag(name = "用户端/作业/作业管理")
@RestController("UserHomeworkManageController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/homework/manage")
@RequiredArgsConstructor
@Slf4j
public class HomeworkController {

    private final PgHomeworkService pgHomeworkService;

    private final PgStudentService pgStudentService;

    private final PgAnswerService pgAnswerService;

    private final PgQuestionService pgQuestionService;

    private final PgExportRecordService pgExportRecordService;

    private final PgClassesService pgClassesService;

    private final PgUserActionService pgUserActionService;

    private final PgHomeworkReportService pgHomeworkReportService;

    private final PgAnswerModelService pgAnswerModelService;

    @Operation(summary = "分页查看班级下的作业列表")
    @PostMapping("page/{classId}")
    @SaCheckLogin
    public BaseResult<IPage<PgHomeworkVO>> pageByClassId(@PathVariable Long classId, @RequestBody HomeworkQuery query) {

        IPage<PgHomeworkVO> page = pgHomeworkService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgHomework>()
                .eq(PgHomework::getCreatorId, StpUtil.getLoginIdAsLong())
                .eq(PgHomework::getClassId, classId)
                .like(StrUtil.isNotBlank(query.getName()), PgHomework::getName, query.getName())

        ).convert(homework -> {
            PgHomeworkVO homeworkVO = BeanUtil.copyProperties(homework, PgHomeworkVO.class);
            // 所属班级信息
            PgClasses classes = pgClassesService.getById(homework.getClassId());
            if (ObjectUtil.isNotNull(classes)) {
                homeworkVO.setClassName(classes.getName());
                homeworkVO.setGrade(classes.getGrade());
                homeworkVO.setClassNum(classes.getClassNum());
            }
            List<Long> studentIds = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                            .eq(PgStudent::getClassId, homework.getClassId())
                    )
                    .stream()
                    .map(PgStudent::getId)
                    .toList();

            if (studentIds.isEmpty()) {
                return homeworkVO;
            }
            List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                    .select(PgAnswer::getId, PgAnswer::getStatus, PgAnswer::getStudentId)
                    .eq(PgAnswer::getDeleted, false)
                    .eq(PgAnswer::getHomeworkId, homework.getId())
                    .in(PgAnswer::getStudentId, studentIds)
            );
            answers = CollUtil.distinct(answers, PgAnswer::getStudentId, false);
            List<Long> submitStudentIds = answers.stream().map(PgAnswer::getStudentId).toList();

            // 【总数】
            homeworkVO.setTotalNum(studentIds.size());

            // 【已交】
            homeworkVO.setSubmitNum(answers.size());

            // 【未交】
            homeworkVO.setUnSubmitNum(
                    (int) studentIds.stream().filter(id -> !submitStudentIds.contains(id)).count()
            );

            //【已批阅】
            homeworkVO.setCorrectedNum((int) answers.stream().filter(answer -> !answer.getStatus().equals(CorrectStatusEnum.Uploaded)).count());

            return homeworkVO;

        });

        return BaseResult.success(page);
    }

    @Operation(summary = "查看班级下的作业列表")
    @PostMapping("list/{classId}")
    @SaCheckLogin
    public BaseResult<List<PgHomeworkVO>> listByClassId(@PathVariable Long classId) {

        List<PgHomeworkVO> list = pgHomeworkService.list(new LambdaQueryWrapper<PgHomework>()
                        .eq(PgHomework::getCreatorId, StpUtil.getLoginIdAsLong())
                        .eq(PgHomework::getClassId, classId)
                ).stream()
                .map(homework -> {

                    PgHomeworkVO homeworkVO = BeanUtil.copyProperties(homework, PgHomeworkVO.class);

                    // 所属班级信息
                    PgClasses classes = pgClassesService.getById(homework.getClassId());
                    if (ObjectUtil.isNotNull(classes)) {
                        homeworkVO.setClassName(classes.getName());
                        homeworkVO.setGrade(classes.getGrade());
                        homeworkVO.setClassNum(classes.getClassNum());
                    }

                    List<Long> studentIds = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                                    .eq(PgStudent::getClassId, homework.getClassId())
                            )
                            .stream()
                            .map(PgStudent::getId)
                            .toList();

                    if (studentIds.isEmpty()) {
                        return homeworkVO;
                    }
                    List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                            .select(PgAnswer::getId, PgAnswer::getStatus, PgAnswer::getStudentId)
                            .eq(PgAnswer::getDeleted, false)
                            .eq(PgAnswer::getHomeworkId, homework.getId())
                            .in(PgAnswer::getStudentId, studentIds)
                    );
                    answers = CollUtil.distinct(answers, PgAnswer::getStudentId, false);
                    List<Long> submitStudentIds = answers.stream().map(PgAnswer::getStudentId).toList();

                    // 【总数】
                    homeworkVO.setTotalNum(studentIds.size());

                    // 【已交】
                    homeworkVO.setSubmitNum(answers.size());

                    // 【未交】
                    homeworkVO.setUnSubmitNum(
                            (int) studentIds.stream().filter(id -> !submitStudentIds.contains(id)).count()
                    );

                    //【已批阅】
                    homeworkVO.setCorrectedNum((int) answers.stream().filter(answer -> !answer.getStatus().equals(CorrectStatusEnum.Uploaded)).count());


                    PgQuestion question = JSONUtil.toBean(JSONUtil.toJsonStr(homework.getQuestionInfo()), PgQuestion.class);

                    if (ObjectUtil.isNotNull(question.getSubject())){
                        homeworkVO.setSubject(question.getSubject());
                    }
                    return homeworkVO;

                })
                .toList();


        return BaseResult.success(list);

    }

    @Operation(summary = "查看作业列表")
    @PostMapping("page")
    @SaCheckLogin
    public BaseResult<IPage<PgHomeworkVO>> page(@RequestBody HomeworkQuery query) {
        // 获取作业列表
        IPage<PgHomeworkVO> page = getPageInfo(query);

        // 如果没有作业
        if (page.getTotal() == 0) {
            // 创建示例作业
            if (pgUserActionService.creatExampleClassHomework(StpUtil.getLoginIdAsLong())) {
                page = getPageInfo(query);
            }
        }

        return BaseResult.success(page);
    }

    private IPage<PgHomeworkVO> getPageInfo(HomeworkQuery query) {

        IPage<PgHomeworkVO> page = pgHomeworkService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgHomework>()
                .eq(PgHomework::getCreatorId, StpUtil.getLoginIdAsLong())
                .like(StrUtil.isNotBlank(query.getName()), PgHomework::getName, query.getName())
        ).convert(homework -> {
            PgHomeworkVO homeworkVO = BeanUtil.copyProperties(homework, PgHomeworkVO.class);

            // 所属班级信息
            PgClasses classes = pgClassesService.getById(homework.getClassId());
            if (ObjectUtil.isNotNull(classes)) {
                homeworkVO.setClassName(classes.getName());
                homeworkVO.setGrade(classes.getGrade());
                homeworkVO.setClassNum(classes.getClassNum());
            }

            // 判断当前登录用户是否是作业的创建者
            homeworkVO.setIsCreator(homework.getCreatorId().equals(StpUtil.getLoginIdAsLong()));

            List<Long> studentIds = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                            .eq(PgStudent::getClassId, homework.getClassId())
                    )
                    .stream()
                    .map(PgStudent::getId)
                    .toList();

            if (studentIds.isEmpty()) {
                return homeworkVO;
            }
            List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                    .select(PgAnswer::getId, PgAnswer::getStatus, PgAnswer::getStudentId)
                    .eq(PgAnswer::getDeleted, false)
                    .eq(PgAnswer::getHomeworkId, homework.getId())
                    .in(PgAnswer::getStudentId, studentIds)
            );

            answers = CollUtil.distinct(answers, PgAnswer::getStudentId, false);
            List<Long> submitStudentIds = answers.stream().map(PgAnswer::getStudentId).toList();

            // 【总数】
            homeworkVO.setTotalNum(studentIds.size());

            // 【已交】
            homeworkVO.setSubmitNum(answers.size());

            // 【未交】
            homeworkVO.setUnSubmitNum(
                    (int) studentIds.stream().filter(id -> !submitStudentIds.contains(id)).count()
            );

            //【已批阅】
            homeworkVO.setCorrectedNum((int) answers.stream().filter(answer -> !answer.getStatus().equals(CorrectStatusEnum.Uploaded)).count());

            return homeworkVO;
        });

        return page;
    }

    @Operation(summary = "发布作业")
    @PostMapping("create")
    public BaseResult<Boolean> create(@RequestBody HomeworkManage manage) {

        if (manage.getClassIds().isEmpty()) {
            return BaseResult.error("请选择班级！");
        }

        // 保存班级-作业关联信息
        for (int i = 0; i < manage.getClassIds().size(); i++) {

            // 保存作业
            PgHomework homework = BeanUtil.copyProperties(manage.getHomeworkInfo(), PgHomework.class);

            homework.setCreatorId(StpUtil.getLoginIdAsLong());
            homework.setCreateTime(new Date());
            // 作业名称
            homework.setName(manage.getHomeworkInfo().getQuestionInfo().getName());
            manage.getHomeworkInfo().getQuestionInfo().setId(null);

            // 科目不能为空
            if (ObjectUtil.isNull(manage.getHomeworkInfo().getQuestionInfo().getSubject())) {
                return BaseResult.error("请选择科目！");
            }

            // 年级不能为空
            if (ObjectUtil.isNull(manage.getHomeworkInfo().getQuestionInfo().getGrade())) {
                return BaseResult.error("请选择年级！");
            }

            // 文体不能为空
            if (ObjectUtil.isNull(manage.getHomeworkInfo().getQuestionInfo().getStyle())) {
                return BaseResult.error("请选择文体！");
            }

            homework.setQuestionInfo(manage.getHomeworkInfo().getQuestionInfo());
            homework.setClassId(manage.getClassIds().get(i));
            pgHomeworkService.save(homework);

            pgHomeworkService.updateById(homework);
        }

        // 判断是否保存至自定义题库
        if (manage.getSaveToCustom()) {
            PgQuestion question = new PgQuestion();

            BeanUtil.copyProperties(manage.getHomeworkInfo().getQuestionInfo(), question, "id");
            question.setIsOfficial(false);
            question.setUserId(StpUtil.getLoginIdAsLong());
            question.setCreateTime(new Date());
            pgQuestionService.save(question);
        }

        return BaseResult.success(true);
    }

    @Operation(summary = "查看作业内容")
    @GetMapping("{homeworkId}")
    public BaseResult<PgHomeworkVO> get(@PathVariable Long homeworkId) {

        PgHomework homework = pgHomeworkService.getById(homeworkId);

        if (ObjectUtil.isNull(homework)) {
            return BaseResult.error(GlobalCode.Item_Null, "作业不存在");
        }

        PgHomeworkVO homeworkVO = BeanUtil.copyProperties(homework, PgHomeworkVO.class);

        PgClasses classes = pgClassesService.getById(homework.getClassId());

        if (ObjectUtil.isNotNull(classes)) {
            // 班级名称
            homeworkVO.setClassName(classes.getName());
        }

        return BaseResult.success(homeworkVO);

    }

    @Operation(summary = "修改作业内容")
    @PutMapping("{homeworkId}")
    public BaseResult<Boolean> update(@PathVariable Long homeworkId, @RequestBody PgHomeworkDTO dto) {

        PgHomework pgHomework = pgHomeworkService.getById(homeworkId);

        if (ObjectUtil.isNull(pgHomework)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }
        PgHomework homework = BeanUtil.copyProperties(dto, PgHomework.class);

        homework.setQuestionInfo(dto.getQuestionInfo());
        homework.setUpdateTime(new Date());
        homework.setName(dto.getQuestionInfo().getName());

        pgHomeworkService.updateById(homework);

        return BaseResult.success(true);
    }

    @Operation(summary = "删除作业")
    @DeleteMapping("{id}")
    public BaseResult<Boolean> delete(@PathVariable Long id) {

        PgHomework homework = pgHomeworkService.getById(id);

        if (ObjectUtil.isNull(homework)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 移除批量导出记录
        pgExportRecordService.remove(new LambdaQueryWrapper<PgExportRecord>()
                .eq(PgExportRecord::getHomeworkId, homework.getId()));

        // 删除作业-提交记录信息 -- 假删除
        List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .select(PgAnswer::getId, PgAnswer::getDeleted, PgAnswer::getHomeworkId)
                .eq(PgAnswer::getHomeworkId, id));
        answers.forEach(answer -> answer.setDeleted(true));
        pgAnswerService.updateBatchById(answers);

        // 删除作业本体
        pgHomeworkService.removeById(id);

        return BaseResult.success(true);
    }

    public record SubmitInfoForm(String studentName, Integer type) {
    }

    @Operation(summary = "查看当前作业下的学生提交情况", description = "上传作业时使用")
    @PostMapping("submitInfo/{homeworkId}")
    @SaCheckLogin
    public BaseResult<List<PgStudentVO>> submitInfo(@PathVariable Long homeworkId,
                                                    @RequestBody SubmitInfoForm form) {

        // 获取作业
        PgHomework homework = pgHomeworkService.getById(homeworkId);
        if (ObjectUtil.isNull(homework)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 获取班级学生
        List<PgStudent> studentList = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                .eq(PgStudent::getClassId, homework.getClassId())
                .and(StrUtil.isNotBlank(form.studentName()), i -> i
                        .like(PgStudent::getName, form.studentName())
                        .or()
                        .like(PgStudent::getStudentNo, form.studentName())
                )
        );

        // 去重
        studentList = CollUtil.distinct(studentList, PgStudent::getId, true);

        List<Long> studentIds = studentList.stream()
                .map(PgStudent::getId)
                .toList();

        if (CollUtil.isEmpty(studentIds)) {
            return BaseResult.success(new ArrayList<>());
        }

        List<PgAnswer> answerList = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .select(PgAnswer::getId, PgAnswer::getStudentId, PgAnswer::getStatus, PgAnswer::getCorrectResult)
                .eq(PgAnswer::getHomeworkId, homeworkId)
                .eq(PgAnswer::getDeleted, false)
        );
        // 获取范文列表
        List<Long> answerModelIds = pgAnswerModelService.list(new LambdaQueryWrapper<PgAnswerModel>()
                .eq(PgAnswerModel::getUserId, StpUtil.getLoginIdAsLong())
                .eq(PgAnswerModel::getHomeworkId, homeworkId)
        ).stream().map(PgAnswerModel::getAnswerId).toList();

        List<PgStudentVO> list;

        // 全部学生
        if (form.type() == 0) {
            list = studentList.stream().map(student -> {
                PgAnswer answer = CollUtil.findOne(answerList, pgAnswer -> pgAnswer.getStudentId().equals(student.getId()));

                PgStudentVO studentVO = BeanUtil.copyProperties(student, PgStudentVO.class);
                studentVO.setHomeworkId(homeworkId);

                // 作答信息
                if (ObjectUtil.isNotNull(answer)) {
                    studentVO.setAnswerId(answer.getId());
                    studentVO.setStatus(answer.getStatus());
                    // 是否是范文
                    studentVO.setIsModel(
                            answerModelIds.contains(answer.getId())
                    );
                    if (!answer.getStatus().equals(CorrectStatusEnum.Uploaded)) {
                        ZwEssayQuestion correctAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);
                        studentVO.setUserScore(Double.valueOf(correctAnswer.getUserScore()));
                    }
                } else {
                    studentVO.setStatus(CorrectStatusEnum.UnSubmit);
                }
                return studentVO;
            }).toList();
        }
        // 未上传学生
        else if (form.type() == 1) {
            // 未上传
            List<Long> unUploadIds = studentIds.stream()
                    .filter(studentId -> !answerList.stream().map(PgAnswer::getStudentId).toList().contains(studentId))
                    .toList();
            if (CollUtil.isEmpty(unUploadIds)) {
                return BaseResult.success(new ArrayList<>());
            }
            list = studentList.stream().filter(student -> unUploadIds.contains(student.getId())).map(student -> {
                PgStudentVO studentVO = BeanUtil.copyProperties(student, PgStudentVO.class);
                studentVO.setStatus(CorrectStatusEnum.UnSubmit);
                studentVO.setHomeworkId(homeworkId);
                return studentVO;
            }).toList();
        }
        // 已上传学生
        else if (form.type() == 2) {
            list = new ArrayList<>();
            for (PgStudent student : studentList) {
                PgAnswer answer = CollUtil.findOne(answerList, pgAnswer -> pgAnswer.getStudentId().equals(student.getId()));

                if (ObjectUtil.isNotNull(answer)) {
                    PgStudentVO studentVO = BeanUtil.copyProperties(student, PgStudentVO.class);
                    studentVO.setHomeworkId(homeworkId);
                    studentVO.setAnswerId(answer.getId());
                    studentVO.setStatus(answer.getStatus());
                    // 是否是范文
                    studentVO.setIsModel(
                            answerModelIds.contains(answer.getId())
                    );
                    if (!answer.getStatus().equals(CorrectStatusEnum.Uploaded)) {
                        ZwEssayQuestion correctAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);
                        studentVO.setUserScore(Double.valueOf(correctAnswer.getUserScore()));
                    }
                    list.add(studentVO);
                }
            }
        }
        // 已批改
        else if (form.type() == 3) {
            list = new ArrayList<>();
            for (PgStudent student : studentList) {
                // 状态为已批改 并按studentId
                PgAnswer answer = CollUtil.findOne(answerList, pgAnswer -> pgAnswer.getStudentId().equals(student.getId())
                        && pgAnswer.getStatus().equals(CorrectStatusEnum.Corrected));

                if (ObjectUtil.isNotNull(answer)) {
                    PgStudentVO studentVO = BeanUtil.copyProperties(student, PgStudentVO.class);
                    studentVO.setHomeworkId(homeworkId);
                    studentVO.setAnswerId(answer.getId());
                    studentVO.setStatus(answer.getStatus());
                    ZwEssayQuestion correctAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);
                    studentVO.setUserScore(Double.valueOf(correctAnswer.getUserScore()));
                    // 是否是范文
                    studentVO.setIsModel(
                            answerModelIds.contains(answer.getId())
                    );
                    list.add(studentVO);
                }
            }
        }
        // 已审核
        else if (form.type() == 4) {
            list = new ArrayList<>();
            for (PgStudent student : studentList) {
                // 状态为已审核 并按studentId
                PgAnswer answer = CollUtil.findOne(answerList, pgAnswer -> pgAnswer.getStudentId().equals(student.getId())
                        && pgAnswer.getStatus().equals(CorrectStatusEnum.Checked));

                if (ObjectUtil.isNotNull(answer)) {
                    PgStudentVO studentVO = BeanUtil.copyProperties(student, PgStudentVO.class);
                    studentVO.setHomeworkId(homeworkId);
                    studentVO.setAnswerId(answer.getId());
                    studentVO.setStatus(answer.getStatus());
                    ZwEssayQuestion correctAnswer = JSONUtil.toBean(answer.getCorrectResult().toString(), ZwEssayQuestion.class);
                    studentVO.setUserScore(Double.valueOf(correctAnswer.getUserScore()));
                    // 是否是范文
                    studentVO.setIsModel(
                            answerModelIds.contains(answer.getId())
                    );
                    list.add(studentVO);
                }
            }
        } else {
            return BaseResult.error("参数错误");
        }

        // 执行排序
        return BaseResult.success(
                pgStudentService.sortByNoName(list)
        );

    }

    @Operation(summary = "根据作业id查看提交记录")
    @GetMapping("record/{homeworkId}")
    @SaCheckLogin
    public BaseResult<List<PgAnswerVO>> record(@PathVariable Long homeworkId) {

        List<PgAnswerVO> list = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                        .eq(PgAnswer::getHomeworkId, homeworkId)
                        .eq(PgAnswer::getUserId, StpUtil.getLoginIdAsLong())
                        .and(i -> i.ne(PgAnswer::getDeleted, true)
                                .or()
                                .isNull(PgAnswer::getDeleted)))
                .stream()
                .map(item -> {
                    ZwEssayQuestion userAnswer = JSONUtil.toBean(item.getAnswer().toString(), ZwEssayQuestion.class);

                    PgAnswerVO answerVO = BeanUtil.copyProperties(item, PgAnswerVO.class, "answer");
                    answerVO.setAnswer(userAnswer);

                    return answerVO;
                }).toList();

        return BaseResult.success(list);
    }

    @Operation(summary = "查看作业批改进度", description = "用于轮询查进度")
    @GetMapping("progress/{homeworkId}")
    public BaseResult<PgHomeworkVO> getProgress(@PathVariable Long homeworkId) {

        PgHomework homework = pgHomeworkService.getById(homeworkId);
        if (ObjectUtil.isNull(homework)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        PgHomeworkVO homeworkVO = BeanUtil.copyProperties(homework, PgHomeworkVO.class);

        List<PgAnswer> answers = pgAnswerService.list(new LambdaQueryWrapper<PgAnswer>()
                .select(PgAnswer::getId, PgAnswer::getStatus)
                .eq(PgAnswer::getDeleted, false)
                .eq(PgAnswer::getHomeworkId, homework.getId()));

        List<Long> studentIds = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                        .eq(PgStudent::getClassId, homework.getClassId()))
                .stream()
                .map(PgStudent::getId)
                .toList();

        // 【提交数量】
        homeworkVO.setSubmitNum(answers.size());

        // 【未交】
        homeworkVO.setUnSubmitNum(studentIds.size() - homeworkVO.getSubmitNum());

        //【已批阅】
        homeworkVO.setCorrectedNum((int) answers.stream().filter(answer -> !answer.getStatus().equals(CorrectStatusEnum.Uploaded)).count());

        return BaseResult.success(homeworkVO);
    }

    @Deprecated
    @Operation(summary = "查看作业未上传的学生列表")
    @GetMapping("unSubmitStudents/{homeworkId}")
    public BaseResult<List<PgStudentVO>> unSubmit(@PathVariable Long homeworkId) {

        // 获取作业
        PgHomework homework = pgHomeworkService.getById(homeworkId);
        if (ObjectUtil.isNull(homework)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 获取班级的学生id
        List<Long> studentIds = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                        .eq(PgStudent::getClassId, homework.getClassId()))
                .stream()
                .map(PgStudent::getId)
                .toList();

        if (studentIds.isEmpty()) {
            return BaseResult.success(new ArrayList<>());
        }

        // answer表中没有student的本次作业的提交记录
        List<PgStudentVO> unSubmitStudents = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                        .in(PgStudent::getId, studentIds))
                .stream()
                .map(student -> BeanUtil.copyProperties(student, PgStudentVO.class))
                .filter(student -> !pgAnswerService.exists(new LambdaQueryWrapper<PgAnswer>()
                        .eq(PgAnswer::getDeleted, false)
                        .eq(PgAnswer::getHomeworkId, homeworkId)
                        .eq(PgAnswer::getStudentId, student.getId())))
                .toList();

        return BaseResult.success(unSubmitStudents);
    }

    @Operation(summary = "生成班级报告")
    @GetMapping("/report/generate/{homeworkId}")
    @SaCheckLogin
    public BaseResult<Boolean> reportGenerate(@PathVariable Long homeworkId) {
        PgHomework homework = pgHomeworkService.getById(homeworkId);

        if (ObjectUtil.isNull(homework)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        boolean exists = pgAnswerService.exists(new LambdaQueryWrapper<PgAnswer>()
                .eq(PgAnswer::getDeleted, false)
                .eq(PgAnswer::getHomeworkId, homeworkId)
                .ne(PgAnswer::getStatus, CorrectStatusEnum.Uploaded)
        );

        if (!exists) {
            return BaseResult.error("当前没有批改完成的作文！");
        }

        boolean exists1 = pgHomeworkReportService.exists(new LambdaQueryWrapper<PgHomeworkReport>()
                .eq(PgHomeworkReport::getHomeworkId, homeworkId)
                .eq(PgHomeworkReport::getUserId, StpUtil.getLoginIdAsLong())
        );
        if (exists1) {
            return BaseResult.error("您已经生成过班级报告了！");
        }

        // 保存记录
        PgHomeworkReport report = new PgHomeworkReport();
        report.setHomeworkId(homeworkId);
        report.setStatus(CorrectStatusEnum.Uploaded);
        report.setCreateTime(new Date());
        report.setUserId(StpUtil.getLoginIdAsLong());

        pgHomeworkReportService.save(report);

        // 发送队列请求
        QueueUtils.addQueueObjectInTransaction(GlobQueueConstants.PGB_XCX_HOMEWORK_REPORT_QUEUE.name(), report.getId());

        return BaseResult.success(true);
    }

    @Operation(summary = "查看班级报告")
    @GetMapping("report/{homeworkId}")
    public BaseResult<PgHomeworkReportVO> report(@PathVariable Long homeworkId) {
        PgHomeworkReport one = pgHomeworkReportService.getOne(new LambdaQueryWrapper<PgHomeworkReport>()
                .eq(PgHomeworkReport::getHomeworkId, homeworkId)
                .last("LIMIT 1")
                .orderByDesc(PgHomeworkReport::getCreateTime)
        );

        if (ObjectUtil.isNull(one)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        if (!one.getStatus().equals(CorrectStatusEnum.Corrected)) {
            return BaseResult.error("报告生成中，请稍后再试！");
        }

        PgHomeworkReportVO vo = BeanUtil.copyProperties(one, PgHomeworkReportVO.class, "report");

        JSONArray data = JSONUtil.parseArray(one.getReport());
        vo.setReport(data);

        // 获取作业名称
        PgHomework homework = pgHomeworkService.getById(homeworkId);
        vo.setHomeworkName(homework.getName());

        return BaseResult.success(vo);
    }

    @Operation(summary = "查看班级报告生成状态")
    @GetMapping("report/status/{homeworkId}")
    public BaseResult<Integer> reportStatus(@PathVariable Long homeworkId) {
        PgHomeworkReport one = pgHomeworkReportService.getOne(new LambdaQueryWrapper<PgHomeworkReport>()
                .eq(PgHomeworkReport::getHomeworkId, homeworkId)
                .last("LIMIT 1")
                .orderByDesc(PgHomeworkReport::getCreateTime)
        );

        if (ObjectUtil.isNull(one)) {
            return BaseResult.success(0);
        }

        if (!one.getStatus().equals(CorrectStatusEnum.Corrected)) {
            return BaseResult.success(1);
        }

        return BaseResult.success(2);
    }

    public record BatchCheckData(List<Long> answerIds) {
    }

    @Operation(summary = "批量审核学生作业")
    @PostMapping("batchCheck")
    public BaseResult<Boolean> batchCheck(@RequestBody BatchCheckData data) {

        if (data.answerIds().isEmpty()) {
            return BaseResult.error("请选择需要审核的学生作业！");
        }

        for (Long answerId : data.answerIds()) {

            PgAnswer answer = pgAnswerService.getById(answerId);

            if (ObjectUtil.isNull(answer)) {
                return BaseResult.code(GlobalCode.Item_Null);
            }

            if (answer.getStatus().equals(CorrectStatusEnum.Uploaded)) {

                return BaseResult.error("当前作业还未批改完成，请稍后审核");
            }

            // 更新状态为 --- 已审核
            answer.setStatus(CorrectStatusEnum.Checked);

            pgAnswerService.updateById(answer);
        }
        return BaseResult.success(true);
    }


}
