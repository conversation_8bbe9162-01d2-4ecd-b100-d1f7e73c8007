package com.pgb.xcx.controller.zc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.service.custom.textbook.TextbookService;
import com.pgb.service.db.PgZcChineseWordService;
import com.pgb.service.db.PgZcEngWordCommonService;
import com.pgb.service.domain.zc.common.textbook.entity.*;
import com.pgb.service.domain.zc.word.TextWordInfo;
import com.pgb.service.domain.zc.text.PgZcText;
import com.pgb.service.domain.zc.text.TextInfo;
import com.pgb.service.domain.zc.text.TextList;
import com.pgb.service.domain.zc.textbook.PgZcTextbook;
import com.pgb.service.domain.zc.textbook.TextbookForm;
import com.pgb.service.domain.zc.textbook.UnitContent;
import com.pgb.service.domain.zc.word.WordItem;
import com.pgb.service.domain.zc.word.chinese.PgZcChineseWord;
import com.pgb.service.db.PgZcTextService;
import com.pgb.service.db.PgZcTextbookService;
import com.pgb.service.domain.zc.word.english.EngWordForm;
import com.pgb.service.domain.zc.word.english.EngWordItem;
import com.pgb.service.domain.zc.word.english.PgZcEnglishWord;
import com.pgb.service.domain.zc.word.english.common.PgZcEngWordCommon;
import com.pgb.service.enums.GenerateStatusEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR>
 * Created by 2025/4/27 19:50
 */
@Tag(name = "用户端/字词/教材")
@RestController("UserZcTextbookController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/zc/textbook")
@RequiredArgsConstructor
@Slf4j
public class TextbookController {

    private final PgZcTextbookService pgZcTextbookService;

    private final PgZcTextService pgZcTextService;

    private final PgZcChineseWordService pgZcChineseWordService;

    private final PgZcEngWordCommonService pgZcEngWordCommonService;

    private final TextbookService textbookService;

    @Operation(summary = "【语文】获取全部单元课文列表")
    @PostMapping("unitList")
    public BaseResult<List<TextList>> unitList(@RequestBody TextbookForm form) {

        // 获取 教材
        PgZcTextbook textbook = pgZcTextbookService.getOne(new LambdaQueryWrapper<PgZcTextbook>()
                .eq(PgZcTextbook::getSystem, form.getSchoolSystem())
                .eq(PgZcTextbook::getGrade, form.getGrade())
                .eq(PgZcTextbook::getVolume, form.getVolume())
                .orderByAsc(PgZcTextbook::getSort)
        );

        // 若教材不存在
        if (ObjectUtil.isNull(textbook)) {
            return BaseResult.success(new ArrayList<>());
        }
        // 获取教材的所含单元
        List<UnitContent> units = JSONUtil.toList(JSONUtil.toJsonStr(textbook.getUnits()), UnitContent.class);

        // 初始化返回内容 --> 单元课文
        List<TextList> list = new ArrayList<>();

        for (UnitContent unit : units) {

            // 获取单元课文 -- 排序
            List<PgZcText> textList = pgZcTextService.list(new LambdaQueryWrapper<PgZcText>()
                    .eq(PgZcText::getTextbookId, textbook.getId())
                    .eq(PgZcText::getUnit, unit.getUnit())
                    // type =0和1的
                    .and(i -> i.eq(PgZcText::getType, 0)
                            .or()
                            .eq(PgZcText::getType, 1))
                    .orderByAsc(PgZcText::getUnit)
                    .orderByAsc(PgZcText::getSort)
            );

            // 遍历构造返回内容
            List<TextInfo> textInfoList = new ArrayList<>();
            for (PgZcText text : textList) {

                // 课文信息
                TextInfo textInfo = new TextInfo();
                textInfo.setId(text.getId());
                // 第几课
                textInfo.setSort(text.getSort());
                // 课文名称
                textInfo.setTextName(text.getName());
                // 识字数量
                textInfo.setKnowWordCount(StrUtil.split(text.getKnowWordIds(), ";").size());
                // 写字数量
                textInfo.setNewWordCount(StrUtil.split(text.getNewWordIds(), ";").size());
                // 词语数量
                textInfo.setCiyuCount(StrUtil.split(text.getCiyuIds(), ";").size());
                // 读读写写 数量
                textInfo.setReadAndWriteCount(StrUtil.split(text.getReadAndWriteIds(), ";").size());

                textInfoList.add(textInfo);
            }

            TextList result = new TextList();

            result.setUnit(unit.getUnitName());
            result.setTextList(textInfoList);

            list.add(result);
        }
        return BaseResult.success(list);
    }

    @Schema(title = "课文字词请求体")
    @Data
    public static class TextWordsForm {

        @Schema(title = "选择的文章")
        private List<Long> textIds;

        @Schema(title = "词汇抽取模式，0：全部，1：抽取词汇")
        private Integer selectType;

        @Schema(title = "抽取词汇数量")
        private Integer selectNum;

        @Schema(title = "字词类型，可多选：1：识字，2：生字，3：词语，4：读读写写")
        private List<Integer> wordTypes;
    }

    @Operation(summary = "【语文】获取指定课文下的字词")
    @PostMapping("textWords")
    public BaseResult<List<TextWordInfo>> textWords(@RequestBody TextWordsForm form) {

        if (CollUtil.isEmpty(form.getTextIds())) {
            return BaseResult.error("请选择词语");
        }

        List<PgZcText> texts = pgZcTextService.list(new LambdaQueryWrapper<PgZcText>()
                .in(PgZcText::getId, form.getTextIds())
                .orderByAsc(PgZcText::getSort)
        );

        // 初始化返回数据
        List<TextWordInfo> textWordInfos = new ArrayList<>();

        // 获取
        for (PgZcText text : texts) {

            List<Long> allWordIds = new ArrayList<>();

            // 识字
            if (form.getWordTypes().contains(1) && StrUtil.isNotBlank(text.getKnowWordIds())) {
                allWordIds.addAll(parseIds(text.getKnowWordIds()));

            }
            // 生字
            if (form.getWordTypes().contains(2) && StrUtil.isNotBlank(text.getNewWordIds())) {
                allWordIds.addAll(parseIds(text.getNewWordIds()));
            }
            // 词语
            if (form.getWordTypes().contains(3) && StrUtil.isNotBlank(text.getCiyuIds())) {
                allWordIds.addAll(parseIds(text.getCiyuIds()));
            }
            // 读读写写
            if (form.getWordTypes().contains(4) && StrUtil.isNotBlank(text.getReadAndWriteIds())) {
                allWordIds.addAll(parseIds(text.getReadAndWriteIds()));
            }

            if (!allWordIds.isEmpty()) {

                // 获取字词
                List<PgZcChineseWord> chineseWords = pgZcChineseWordService.list(new LambdaQueryWrapper<PgZcChineseWord>()
                        .in(PgZcChineseWord::getId, allWordIds)
                );

                TextWordInfo word = new TextWordInfo();

                // 第几课
                if (StrUtil.isNotBlank(text.getLesson())) {
                    word.setLesson(text.getLesson());
                }

                // 课文名称
                word.setTextName(text.getName());

                List<WordItem> items = new ArrayList<>();

                for (PgZcChineseWord chineseWord : chineseWords) {

                    // 字词列表
                    WordItem wordsItem = new WordItem();
                    wordsItem.setWord(chineseWord.getWord());
                    wordsItem.setPinyin(chineseWord.getPinyin());

                    items.add(wordsItem);
                    word.setWordList(items);
                }
                textWordInfos.add(word);
            }
        }

        // 抽词
        if (form.getSelectType() == 1) {
            // 校验抽取数量
            if (form.getSelectNum() == null || form.getSelectNum() <= 0) {
                return BaseResult.error("请指定正确的抽取数量");
            }

            // 收集所有词汇及对应的课文
            List<WordItem> allWords = new ArrayList<>();
            Map<WordItem, TextWordInfo> wordToTextMap = new HashMap<>();
            for (TextWordInfo textInfo : textWordInfos) {
                for (WordItem word : textInfo.getWordList()) {
                    allWords.add(word);
                    wordToTextMap.put(word, textInfo);
                }
            }

            // 计算实际抽取数量（不超过总词汇数）
            int actualSelectNum = Math.min(form.getSelectNum(), allWords.size());
            if (actualSelectNum == 0) {
                return BaseResult.success(Collections.emptyList());
            }

            // 随机打乱并抽取
            Collections.shuffle(allWords);
            List<WordItem> selectedWords = allWords.subList(0, actualSelectNum);

            // 清空原有词汇，重新分配选中词汇
            textWordInfos.forEach(text -> text.setWordList(new ArrayList<>()));
            selectedWords.forEach(word -> {
                TextWordInfo textInfo = wordToTextMap.get(word);
                if (textInfo != null) {
                    textInfo.getWordList().add(word);
                }
            });
        }

        return BaseResult.success(textWordInfos);
    }

    /**
     * 按;分割，获取字词ids
     *
     * @param idsStr
     * @return
     */
    private List<Long> parseIds(String idsStr) {
        return Arrays.stream(idsStr.split(";"))
                .map(Long::valueOf)
                .toList();
    }

    @Operation(summary = "【语文】获取课文列表", description = "需要默写的和填空的")
    @PostMapping("textList")
    public BaseResult<List<PgZcText>> textList(@RequestBody TextbookForm form) {

        // 获取 教材
        PgZcTextbook textbook = pgZcTextbookService.getOne(new LambdaQueryWrapper<PgZcTextbook>()
                .eq(StrUtil.isNotBlank(form.getSchoolSystem()), PgZcTextbook::getSystem, form.getSchoolSystem())
                .eq(PgZcTextbook::getGrade, form.getGrade())
                .eq(PgZcTextbook::getVolume, form.getVolume())
                .orderByAsc(PgZcTextbook::getSort)
        );

        if (ObjectUtil.isNull(textbook)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 获取单元课文 -- 排序
        List<PgZcText> textList = pgZcTextService.list(new LambdaQueryWrapper<PgZcText>()
                .eq(PgZcText::getTextbookId, textbook.getId())
                // 需要背诵内容不为空的
                .isNotNull(PgZcText::getContent)
                .orderByAsc(PgZcText::getUnit)
                .orderByAsc(PgZcText::getSort)
        );

        return BaseResult.success(textList);
    }

    @Operation(summary = "【英语】获取单词的音标和释义")
    @PostMapping("english/word")
    public BaseResult<List<EngWordItem>> englishWord(@RequestBody EngWordForm form) {

        TimeInterval interval = DateUtil.timer();

        // 根据换行分割
        List<String> words = Arrays.stream(form.getWords().split("\\n")).toList();

        // 只要英语单词
        List<EngWordItem> engList = words.stream()
                .map(item -> ReUtil.delAll("[^a-zA-Z' -()]", item))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .map(word -> {

                    // 先查询数据库是否存在
                    PgZcEngWordCommon enWordInfo = pgZcEngWordCommonService.getOne(new LambdaQueryWrapper<PgZcEngWordCommon>()
                            .eq(PgZcEngWordCommon::getWord, word)
                            .last("limit 1")
                    );
                    if (ObjectUtil.isNull(enWordInfo)) {
                        enWordInfo = new PgZcEngWordCommon();
                        enWordInfo.setWord(word);
                        enWordInfo.setStatus(GenerateStatusEnum.Init);
                        pgZcEngWordCommonService.save(enWordInfo);
                    }
                    // 调用LLM获取单词信息
                    enWordInfo = pgZcEngWordCommonService.getEnWordInfo(enWordInfo);

                    EngWordItem item = new EngWordItem();
                    item.setWord(enWordInfo.getWord());
                    item.setPhonetic(enWordInfo.getPhonetic());
                    item.setChinese(enWordInfo.getChinese());
                    item.setWordClass(enWordInfo.getWordClass());
                    return item;

                })
                .toList();

        log.info("生成单词音标内容：用时：{}，大模型生成单词数量：{}个", interval.intervalPretty(), engList.size());

        return BaseResult.success(engList);
    }


    @Operation(summary = "【英语】获取教材类型列表")
    @PostMapping("english/list")
    public BaseResult<List<PgTextbookType>> englishTextbook() {

        return textbookService.getTextbookTypeList();
    }

    @Operation(summary = "【英语】获取年级列表")
    @GetMapping("grade/list")
    public BaseResult<List<PgTextbookGrade>> gradeList(
            @Parameter(description = "教材类型ID") @RequestParam Long versionId) {
        return textbookService.getTextbookGradeList(versionId);
    }

    @Operation(summary = "【英语】获取版本列表")
    @GetMapping("/version/list")
    public BaseResult<List<PgTextbookVersion>> getTextbookVersionList(
            @Parameter(description = "年级ID") @RequestParam Long typeId) {
        return textbookService.getTextbookVersionList(typeId);
    }

    @Operation(summary = "【英语】获取册别列表")
    @GetMapping("/volume/list")
    public BaseResult<List<PgTextbookVolume>> getTextbookVolumeList(
            @Parameter(description = "版本ID") @RequestParam Long gradeId) {
        return textbookService.getTextbookVolumeList(gradeId);
    }

    @Operation(summary = "【英语】获取单元列表")
    @GetMapping("/unit/list")
    public BaseResult<List<PgTextbookUnit>> getTextbookUnitList(
            @Parameter(description = "册别ID") @RequestParam Long volumeId) {
        return textbookService.getTextbookUnitList(volumeId);
    }


}
