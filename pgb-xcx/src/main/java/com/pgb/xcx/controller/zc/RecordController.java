package com.pgb.xcx.controller.zc;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.common.core.utils.FieldUtils;
import com.pgb.common.mybatis.global.PageQuery;
import com.pgb.service.db.*;
import com.pgb.service.domain.answer.PgAnswer;
import com.pgb.service.domain.classes.PgClasses;
import com.pgb.service.domain.student.PgStudent;
import com.pgb.service.domain.student.PgStudentVO;
import com.pgb.service.domain.zc.answer.PgZcAnswer;
import com.pgb.service.domain.zc.answer.PgZcAnswerVO;
import com.pgb.service.domain.zc.answer.batch.PgZcAnswerBatch;
import com.pgb.service.domain.zc.answer.batch.PgZcAnswerBatchVO;
import com.pgb.service.domain.zc.homework.PgZcHomework;
import com.pgb.service.domain.zc.homework.PgZcHomeworkVO;
import com.pgb.service.domain.zc.question.PgZcQuestion;
import com.pgb.service.domain.zc.question.chinese.ZcSubmitForm;
import com.pgb.service.domain.zc.question.chinese.pinyinAndWord.PinyinAndWordResult;
import com.pgb.service.domain.zc.question.ZcQuestion;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.enums.SubjectEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/4/22 16:29
 */
@Tag(name = "用户端/字词/批改记录")
@RestController("UserZcRecordController")
@Transactional(rollbackFor = Exception.class)
@RequestMapping("/user/zc/record")
@RequiredArgsConstructor
@Slf4j
public class RecordController {

    private final PgZcAnswerService pgZcAnswerService;

    private final PgZcAnswerBatchService pgZcAnswerBatchService;

    private final PgZcQuestionService pgZcQuestionService;

    private final PgZcHomeworkService pgZcHomeworkService;

    private final PgClassesService pgClassesService;

    private final PgStudentService pgStudentService;

    @Operation(summary = "删除单篇字词作业提交记录")
    @DeleteMapping("delete/{id}")
    @SaCheckLogin
    public BaseResult<Boolean> delete(@PathVariable Long id) {

        PgZcAnswer zcAnswer = pgZcAnswerService.getById(id);

        if (!zcAnswer.getUserId().equals(StpUtil.getLoginIdAsLong())) {
            return BaseResult.error("当前无权限删除作文内容");
        }

        if (ObjectUtil.isNull(zcAnswer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }
        // 标记为删除
        zcAnswer.setDeleted(true);
        pgZcAnswerService.updateById(zcAnswer);

        // 判断是否有批次
        if (ObjectUtil.isNotNull(zcAnswer.getBatchId())) {

            // 查看该批次下是否还有提交记录
            long count = pgZcAnswerService.count(new LambdaQueryWrapper<PgZcAnswer>()
                    .eq(PgZcAnswer::getBatchId, zcAnswer.getBatchId())
                    .ne(PgZcAnswer::getDeleted, true)
            );

            // 如果没有 则删除批次
            if (count == 0) {
                pgZcAnswerBatchService.removeById(zcAnswer.getBatchId());
            }
        }
        return BaseResult.success(true);
    }


    @Data
    public static class BatchPreNextForm {

        @Schema(title = "当前answerId")
        private Long currentAnswerId;

        @Schema(title = "0：上一份，1：下一份")
        private Integer preOrNext;
    }

    @Operation(summary = "查看批次的上一份下一份")
    @PostMapping("batch/preNext/{batchId}")
    public BaseResult<PgZcAnswerVO> batchPreNext(@PathVariable Long batchId, @RequestBody BatchPreNextForm form) {

        // 查询批次下的所有批改记录
        List<PgZcAnswer> answers = pgZcAnswerService.list(new LambdaQueryWrapper<PgZcAnswer>()
                .eq(PgZcAnswer::getBatchId, batchId)
                .eq(PgZcAnswer::getUserId, StpUtil.getLoginIdAsLong())
                .and(i -> i.ne(PgZcAnswer::getDeleted, true)
                        .or()
                        .isNull(PgZcAnswer::getDeleted)
                )
                .orderByDesc(PgZcAnswer::getCreateTime)
        );

        if (CollUtil.isEmpty(answers)) {
            return BaseResult.error(GlobalCode.Item_Null, "该批次下没有作答记录");
        }

        // 查找当前记录的索引
        int currentIndex = -1;
        for (int i = 0; i < answers.size(); i++) {

            if (answers.get(i).getId().equals(form.getCurrentAnswerId())) {

                currentIndex = i;
                break;
            }
        }

        // 目标索引
        int targetIndex;

        // 上一份
        if (form.getPreOrNext().equals(0)) {

            if (currentIndex <= 0) {
                return BaseResult.error(GlobalCode.Item_Null, "没有上一份了");
            }
            targetIndex = currentIndex - 1;
        }
        // 下一份
        else {
            if (currentIndex >= answers.size() - 1) {
                return BaseResult.error(GlobalCode.Item_Null, "没有下一份了");
            }
            targetIndex = currentIndex + 1;
        }

        // 获取目标记录并转换为VO
        PgZcAnswer pgZcAnswer = answers.get(targetIndex);
        PgZcAnswerVO answerVO = BeanUtil.copyProperties(pgZcAnswer, PgZcAnswerVO.class, "answer", "correctResult");

        answerVO.setAnswer(
                JSONUtil.toBean(pgZcAnswer.getAnswer().toString(), ZcSubmitForm.class)
        );
        answerVO.setCorrectResult(
                JSONUtil.toBean(pgZcAnswer.getCorrectResult().toString(), PinyinAndWordResult.class)
        );

        return BaseResult.success(answerVO);

    }


    @Operation(summary = "查看单篇批改状态")
    @PostMapping("status/{id}")
    public BaseResult<CorrectStatusEnum> status(@PathVariable Long id) {

        PgZcAnswer zcAnswer = pgZcAnswerService.getById(id);

        if (ObjectUtil.isNull(zcAnswer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }
        return BaseResult.success(zcAnswer.getStatus());
    }


    @Operation(summary = "查看单篇批改结果")
    @PostMapping("correct/result/{id}")
    public BaseResult<PgZcAnswerVO> correctResult(@PathVariable Long id) {
        PgZcAnswer answer = pgZcAnswerService.getById(id);

        if (ObjectUtil.isNull(answer)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        PgZcAnswerVO answerVO = new PgZcAnswerVO();
        BeanUtil.copyProperties(answer, answerVO, "answer", "correctResult");

        answerVO.setAnswer(
                JSONUtil.toBean(answer.getAnswer().toString(), ZcSubmitForm.class)
        );
        answerVO.setCorrectResult(
                JSONUtil.toBean(answer.getCorrectResult().toString(), PinyinAndWordResult.class)
        );

        return BaseResult.success(answerVO);
    }

    @Operation(summary = "保存批改结果")
    @PostMapping("correct/saveResult/{answerId}")
     @SaCheckLogin
    public BaseResult<Boolean> saveResult(@RequestBody PgZcAnswerVO answerVO, @PathVariable Long answerId) {

        PgZcAnswer answer = pgZcAnswerService.getById(answerId);

        // // 非本人不能保存
        // if (StpUtil.getLoginIdAsLong() != answer.getUserId()) {
        //     return BaseResult.code(GlobalCode.Permission_Not);
        // }

        // 拿数据
        PinyinAndWordResult correctResult = answerVO.getCorrectResult();

        answer.setCorrectResult(correctResult);

        pgZcAnswerService.updateById(answer);

        return BaseResult.success(true);
    }


    @Operation(summary = "根据题目查看自由批改批次记录")
    @PostMapping("batch/page/{zcQuesId}")
    @SaCheckLogin
    public BaseResult<IPage<PgZcAnswerBatchVO>> batchByQues(@RequestBody PageQuery query, @PathVariable Long zcQuesId) {

        LambdaQueryWrapper<PgZcAnswerBatch> queryWrapper = new LambdaQueryWrapper<PgZcAnswerBatch>()
                .eq(PgZcAnswerBatch::getZcQuestionId, zcQuesId)
                .eq(PgZcAnswerBatch::getUserId, StpUtil.getLoginIdAsLong());

        IPage<PgZcAnswerBatchVO> page = pgZcAnswerBatchService.page(query.toMpPageSortByCreateTime(), queryWrapper
        ).convert(batch -> {

            PgZcAnswerBatchVO batchVO = BeanUtil.copyProperties(batch, PgZcAnswerBatchVO.class);

            List<PgZcAnswer> answers = pgZcAnswerService.list(new LambdaQueryWrapper<PgZcAnswer>()
                    .select(PgZcAnswer::getId, PgZcAnswer::getStatus)
                    .eq(PgZcAnswer::getBatchId, batch.getId())
                    .eq(PgZcAnswer::getDeleted, false)
            );

            // 总数
            long totalNum = answers.size();
            batchVO.setTotalNum((int) totalNum);

            // 已批改数量
            long correctedNum = CollUtil.count(answers, answer -> answer.getStatus() == CorrectStatusEnum.Corrected);
            batchVO.setCorrectedNum((int) correctedNum);

            // 题目类型
            if (!answers.isEmpty()) {
                batchVO.setQuesType(answers.get(0).getQuesType());
            }

            // 【批量提交】题目信息
            if (ObjectUtil.isNotNull(batch.getZcQuestionId())) {
                PgZcQuestion zcQuestion = pgZcQuestionService.getById(batch.getZcQuestionId());
                if (ObjectUtil.isNotNull(zcQuestion)) {
                    batchVO.setQuestionName(zcQuestion.getName());
                }
            }
            return batchVO;
        });

        return BaseResult.success(page);

    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class ZcAnswerBatchQuery extends PageQuery {

        @Schema(title = "科目")
        private SubjectEnum subject;

    }

    @Operation(summary = "查看自由批改批次记录")
    @PostMapping("batch/page")
    @SaCheckLogin
    public BaseResult<IPage<PgZcAnswerBatchVO>> batchPage(@RequestBody ZcAnswerBatchQuery query) {

        LambdaQueryWrapper<PgZcAnswerBatch> queryWrapper = new LambdaQueryWrapper<PgZcAnswerBatch>()
                .eq(PgZcAnswerBatch::getUserId, StpUtil.getLoginIdAsLong());

        // 根据科目筛选
        if (ObjectUtil.isNotNull(query.getSubject())) {

            List<Long> zcQuestionIds = pgZcQuestionService.list(new LambdaQueryWrapper<PgZcQuestion>()
                            .eq(PgZcQuestion::getSubject, query.getSubject()))
                    .stream()
                    .map(PgZcQuestion::getId)
                    .toList();

            if (CollUtil.isEmpty(zcQuestionIds)) {
                return BaseResult.success(new Page<>());
            }
            queryWrapper.in(PgZcAnswerBatch::getZcQuestionId, zcQuestionIds);
        }

        IPage<PgZcAnswerBatchVO> page = pgZcAnswerBatchService.page(query.toMpPageSortByCreateTime(), queryWrapper
        ).convert(batch -> {

            PgZcAnswerBatchVO batchVO = BeanUtil.copyProperties(batch, PgZcAnswerBatchVO.class);

            List<PgZcAnswer> answers = pgZcAnswerService.list(new LambdaQueryWrapper<PgZcAnswer>()
                    .select(PgZcAnswer::getId, PgZcAnswer::getStatus)
                    .eq(PgZcAnswer::getBatchId, batch.getId())
                    .eq(PgZcAnswer::getDeleted, false)
            );

            // 总数
            long totalNum = answers.size();
            batchVO.setTotalNum((int) totalNum);

            // 已批改数量
            long correctedNum = CollUtil.count(answers, answer -> answer.getStatus() == CorrectStatusEnum.Corrected);
            batchVO.setCorrectedNum((int) correctedNum);

            // 题目类型
            if (!answers.isEmpty()) {
                batchVO.setQuesType(answers.get(0).getQuesType());
            }

            // 【批量提交】题目信息
            if (ObjectUtil.isNotNull(batch.getZcQuestionId())) {
                PgZcQuestion zcQuestion = pgZcQuestionService.getById(batch.getZcQuestionId());
                if (ObjectUtil.isNotNull(zcQuestion)) {
                    batchVO.setQuestionName(zcQuestion.getName());
                }
            }
            return batchVO;
        });

        return BaseResult.success(page);

    }

    @Operation(summary = "查看批次进度", description = "用于轮询查进度")
    @GetMapping("batchProgress/{batchId}")
    @SaCheckLogin
    public BaseResult<PgZcAnswerBatchVO> batchProgress(@PathVariable Long batchId) {

        PgZcAnswerBatch batch = pgZcAnswerBatchService.getById(batchId);

        if (ObjectUtil.isNull(batch)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        PgZcAnswerBatchVO batchVO = BeanUtil.copyProperties(batch, PgZcAnswerBatchVO.class);

        LambdaQueryWrapper<PgZcAnswer> wrapper = new LambdaQueryWrapper<PgZcAnswer>()
                .eq(PgZcAnswer::getBatchId, batchId)
                .and(i -> i.ne(PgZcAnswer::getDeleted, true)
                        .or()
                        .isNull(PgZcAnswer::getDeleted));

        // 总数
        long totalNum = pgZcAnswerService.count(wrapper);
        batchVO.setTotalNum((int) totalNum);

        // 已批改数量
        long correctedNum = pgZcAnswerService.count(wrapper
                .ne(PgZcAnswer::getStatus, CorrectStatusEnum.Uploaded)
        );
        batchVO.setCorrectedNum((int) correctedNum);

        return BaseResult.success(batchVO);
    }

    @Operation(summary = "自由批改记录", description = "根据批次查")
    @PostMapping("listByBatchId/{batchId}")
    @SaCheckLogin
    public BaseResult<List<PgZcAnswerVO>> pageByBatchId(@PathVariable Long batchId) {

        List<PgZcAnswerVO> list = pgZcAnswerService.list(new LambdaQueryWrapper<PgZcAnswer>()
                        .eq(PgZcAnswer::getBatchId, batchId)
                        .eq(PgZcAnswer::getUserId, StpUtil.getLoginIdAsLong())
                        .and(i -> i.ne(PgZcAnswer::getDeleted, true)
                                .or()
                                .isNull(PgZcAnswer::getDeleted)
                        )
                        .orderByDesc(PgZcAnswer::getCreateTime)

                ).stream()
                .map(pgZcAnswer -> {
                    ZcSubmitForm userAnswer = JSONUtil.toBean(pgZcAnswer.getAnswer().toString(), ZcSubmitForm.class);

                    PgZcAnswerVO pgZcAnswerVO = BeanUtil.copyProperties(pgZcAnswer, PgZcAnswerVO.class,
                            FieldUtils.getFieldName(PgZcAnswer::getAnswer),
                            FieldUtils.getFieldName(PgZcAnswer::getCorrectResult)
                    );
                    pgZcAnswerVO.setAnswer(userAnswer);

                    return pgZcAnswerVO;
                })
                .toList();
        return BaseResult.success(list);
    }

    @Operation(summary = "班级批改记录")
    @PostMapping("zcHomework/page")
    @SaCheckLogin
    public BaseResult<IPage<PgZcHomeworkVO>> zcHomeworkPage(@RequestBody PageQuery query) {

        IPage<PgZcHomeworkVO> page = pgZcHomeworkService.page(query.toMpPageSortByCreateTime(), new LambdaQueryWrapper<PgZcHomework>()
                .eq(PgZcHomework::getUserId, StpUtil.getLoginIdAsLong())
        ).convert(zcHomework -> {
            PgZcHomeworkVO zcHomeworkVO = BeanUtil.copyProperties(zcHomework, PgZcHomeworkVO.class);

            // 所属班级信息
            PgClasses classes = pgClassesService.getById(zcHomework.getClassId());
            if (ObjectUtil.isNotNull(classes)) {
                zcHomeworkVO.setClassName(classes.getName());
            }
            // 学生id列表
            List<Long> studentIds = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                            .eq(PgStudent::getClassId, zcHomework.getClassId())
                    )
                    .stream()
                    .map(PgStudent::getId)
                    .toList();

            if (studentIds.isEmpty()) {
                return zcHomeworkVO;
            }

            List<PgZcAnswer> answers = pgZcAnswerService.list(new LambdaQueryWrapper<PgZcAnswer>()
                    .select(PgZcAnswer::getId, PgZcAnswer::getStatus, PgZcAnswer::getStudentId)
                    .eq(PgZcAnswer::getDeleted, false)
                    .eq(PgZcAnswer::getZcHomeworkId, zcHomework.getId())
                    .in(PgZcAnswer::getStudentId, studentIds)
            );
            answers = CollUtil.distinct(answers, PgZcAnswer::getStudentId, false);
            List<Long> submitStudentIds = answers.stream().map(PgZcAnswer::getStudentId).toList();

            // 【已提交】
            zcHomeworkVO.setSubmitNum(answers.size());

            // 【未提交】
            zcHomeworkVO.setUnSubmitNum((int) studentIds.stream().filter(id -> !submitStudentIds.contains(id)).count());

            // 【已批改】
            zcHomeworkVO.setCorrectedNum((int) answers.stream().filter(answer -> !answer.getStatus().equals(CorrectStatusEnum.Uploaded)).count());

            return zcHomeworkVO;
        });

        return BaseResult.success(page);
    }


    public record SubmitInfoForm(String studentInfo, Integer type) {
    }

    @Operation(summary = "查看作业的学生提交情况", description = "上传作业时使用")
    @GetMapping("submitInfo/{zcHomeworkId}")
    @SaCheckLogin
    public BaseResult<List<PgStudentVO>> submitInfo(@PathVariable Long zcHomeworkId, @RequestBody SubmitInfoForm form) {

        // 获取作业
        PgZcHomework zcHomework = pgZcHomeworkService.getById(zcHomeworkId);

        if (ObjectUtil.isNull(zcHomework)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 获取班级学生
        List<PgStudent> students = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                .eq(PgStudent::getClassId, zcHomework.getClassId())
                .and(StrUtil.isNotBlank(form.studentInfo()), i -> i
                        .like(PgStudent::getName, form.studentInfo())
                        .or()
                        .like(PgStudent::getStudentNo, form.studentInfo())
                )
        );

        // 去重
        students = CollUtil.distinct(students, PgStudent::getId, true);

        List<Long> studentIds = students.stream()
                .map(PgStudent::getId)
                .toList();

        if (CollUtil.isEmpty(studentIds)) {
            return BaseResult.success(new ArrayList<>());
        }

        List<PgZcAnswer> answerList = pgZcAnswerService.list(new LambdaQueryWrapper<PgZcAnswer>()
                .select(PgZcAnswer::getId, PgZcAnswer::getStudentId, PgZcAnswer::getStatus, PgZcAnswer::getCorrectResult)
                .eq(PgZcAnswer::getZcHomeworkId, zcHomeworkId)
                .eq(PgZcAnswer::getDeleted, false)
        );

        List<PgStudentVO> list;

        // 全部学生
        if (form.type() == 0) {

            list = students.stream().map(student -> {
                PgZcAnswer zcAnswer = CollUtil.findOne(answerList, pgZcAnswer -> pgZcAnswer.getStudentId().equals(student.getId()));

                PgStudentVO studentVO = BeanUtil.copyProperties(student, PgStudentVO.class);
                studentVO.setHomeworkId(zcHomeworkId);

                // 作答信息
                if (ObjectUtil.isNotNull(zcAnswer)) {
                    studentVO.setAnswerId(zcAnswer.getId());
                    studentVO.setStatus(zcAnswer.getStatus());
                    if (!zcAnswer.getStatus().equals(CorrectStatusEnum.Uploaded)) {
                        ZcQuestion correctAnswer = JSONUtil.toBean(zcAnswer.getCorrectResult().toString(), ZcQuestion.class);
                        if (ObjectUtil.isNotNull(correctAnswer.getUserScore())) {
                            studentVO.setUserScore(correctAnswer.getUserScore());
                        }
                    }
                } else {
                    studentVO.setStatus(CorrectStatusEnum.UnSubmit);
                }
                return studentVO;
            }).toList();
        }
        // 未提交学生
        else if (form.type() == 1) {

            List<Long> unSubmitIds = studentIds.stream()
                    .filter(studentId -> !answerList.stream().map(PgZcAnswer::getStudentId).toList().contains(studentId))
                    .toList();
            if (CollUtil.isEmpty(unSubmitIds)) {
                return BaseResult.success(new ArrayList<>());
            }

            list = students.stream().filter(student -> unSubmitIds.contains(student.getId())).map(student -> {
                PgStudentVO studentVO = BeanUtil.copyProperties(student, PgStudentVO.class);
                studentVO.setStatus(CorrectStatusEnum.UnSubmit);
                studentVO.setHomeworkId(zcHomeworkId);
                return studentVO;
            }).toList();
        }
        // 已上传学生
        else if (form.type() == 2) {
            list = new ArrayList<>();
            for (PgStudent student : students) {
                PgZcAnswer zcAnswer = CollUtil.findOne(answerList, pgZcAnswer -> pgZcAnswer.getStudentId().equals(student.getId()));
                if (ObjectUtil.isNotNull(zcAnswer)) {
                    PgStudentVO studentVO = BeanUtil.copyProperties(student, PgStudentVO.class);
                    studentVO.setHomeworkId(zcHomeworkId);
                    studentVO.setAnswerId(zcAnswer.getId());
                    studentVO.setStatus(zcAnswer.getStatus());
                    // 用户分数
//                    if (!zcAnswer.getStatus().equals(CorrectStatusEnum.Uploaded)) {
//                        ZcQuestion correctAnswer = JSONUtil.toBean(zcAnswer.getCorrectResult().toString(), ZcQuestion.class);
//                        if (ObjectUtil.isNotNull(correctAnswer.getUserScore())) {
//                            studentVO.setUserScore(correctAnswer.getUserScore());
//                        }
//                    }
                    list.add(studentVO);
                }
            }
        } else {
            return BaseResult.error("参数错误");
        }
        // 执行排序
        return BaseResult.success(
                pgStudentService.sortByNoName(list)
        );

    }


    @Operation(summary = "查看作业批改进度", description = "用于轮询查进度")
    @GetMapping("homeworkProgress/{zcHomeworkId}")
    public BaseResult<PgZcHomeworkVO> getProgress(@PathVariable Long zcHomeworkId) {

        PgZcHomework zcHomework = pgZcHomeworkService.getById(zcHomeworkId);

        if (ObjectUtil.isNull(zcHomework)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        PgZcHomeworkVO zcHomeworkVO = BeanUtil.copyProperties(zcHomework, PgZcHomeworkVO.class);

        List<PgZcAnswer> answers = pgZcAnswerService.list(new LambdaQueryWrapper<PgZcAnswer>()
                .select(PgZcAnswer::getId, PgZcAnswer::getStatus)
                .eq(PgZcAnswer::getZcHomeworkId, zcHomeworkId)
                .eq(PgZcAnswer::getDeleted, false)
        );

        List<Long> studentIds = pgStudentService.list(new LambdaQueryWrapper<PgStudent>()
                        .eq(PgStudent::getClassId, zcHomework.getClassId()))
                .stream()
                .map(PgStudent::getId)
                .toList();

        //【已提交】
        zcHomeworkVO.setSubmitNum(answers.size());

        //【未提交】
        zcHomeworkVO.setUnSubmitNum(studentIds.size() - zcHomeworkVO.getSubmitNum());

        //【已批改】
        zcHomeworkVO.setCorrectedNum(
                (int) answers.stream().filter(answer -> !answer.getStatus().equals(CorrectStatusEnum.Uploaded)).count()
        );
        return BaseResult.success(zcHomeworkVO);
    }

    @Operation(summary = "删除自由批改记录")
    @DeleteMapping("deleteBatch/{batchId}")
    @SaCheckLogin
    public BaseResult<Boolean> deleteBatch(@PathVariable Long batchId) {

        PgZcAnswerBatch batch = pgZcAnswerBatchService.getById(batchId);

        if (ObjectUtil.isNull(batch)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        List<PgZcAnswer> answers = pgZcAnswerService.list(new LambdaQueryWrapper<PgZcAnswer>()
                .select(PgZcAnswer::getId, PgZcAnswer::getBatchId, PgZcAnswer::getDeleted)
                .eq(PgZcAnswer::getBatchId, batchId)
        );

        answers.forEach(answer -> answer.setDeleted(true));

        pgZcAnswerService.updateBatchById(answers);

        return BaseResult.success(
                pgZcAnswerBatchService.removeById(batchId)
        );
    }

    @Operation(summary = "删除班级批改记录", description = "删除作业")
    @DeleteMapping("deleteHomework/{zcHomeworkId}")
    @SaCheckLogin
    public BaseResult<Boolean> deleteHomework(@PathVariable Long zcHomeworkId) {

        PgZcHomework zcHomework = pgZcHomeworkService.getById(zcHomeworkId);

        if (ObjectUtil.isNull(zcHomework)) {
            return BaseResult.code(GlobalCode.Item_Null);
        }

        // 删除作答记录 -- 假删除
        List<PgZcAnswer> answers = pgZcAnswerService.list(new LambdaQueryWrapper<PgZcAnswer>()
                .select(PgZcAnswer::getId, PgZcAnswer::getZcHomeworkId, PgZcAnswer::getDeleted)
                .eq(PgZcAnswer::getZcHomeworkId, zcHomeworkId)
        );
        answers.forEach(answer -> answer.setDeleted(true));
        pgZcAnswerService.updateBatchById(answers);

        // 删除作业
        return BaseResult.success(
                pgZcHomeworkService.removeById(zcHomeworkId)
        );
    }

}
