package com.pgb.manage.controller.textbook;

import cn.dev33.satoken.annotation.SaCheckRole;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.RoleConstants;
import com.pgb.service.custom.textbook.TextbookService;
import com.pgb.service.domain.zc.common.textbook.dto.*;
import com.pgb.service.domain.zc.common.textbook.entity.*;
import com.pgb.service.domain.zc.common.textbook.vo.IdResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 教材管理Controller
 *
 * <AUTHOR>
 * Created by 2025/7/25 18:34
 */
@Tag(name = "管理端/教材管理", description = "教材类型、年级、版本、册别、单元管理接口")
@Slf4j
@RestController("ManageTextbookController")
@RequestMapping("/manage/textbook")
@RequiredArgsConstructor
@SaCheckRole(RoleConstants.Manager)
public class TextbookController {

    private final TextbookService textbookService;

    // ==================== 教材类型相关接口 ====================

    @Operation(summary = "获取教材类型列表")
    @GetMapping("/type/list")
    public BaseResult<List<PgTextbookType>> getTextbookTypeList() {
//        log.info("【内部管理】获取教材类型列表");
        return textbookService.getTextbookTypeList();
    }

    @Operation(summary = "创建教材类型")
    @PostMapping("/type")
    public BaseResult<IdResultVO> createTextbookType(@RequestBody @Validated PgTextbookTypeDTO dto) {
        return textbookService.createTextbookType(dto);
    }

    @Operation(summary = "更新教材类型")
    @PutMapping("/type/{id}")
    public BaseResult<Boolean> updateTextbookType(
            @Parameter(description = "教材类型ID") @PathVariable Long id,
            @RequestBody @Validated PgTextbookTypeDTO dto) {
        return textbookService.updateTextbookType(id, dto);
    }

    @Operation(summary = "删除教材类型")
    @DeleteMapping("/type/{id}")
    public BaseResult<Boolean> deleteTextbookType(
            @Parameter(description = "教材类型ID") @PathVariable Long id) {
        return textbookService.deleteTextbookType(id);
    }

    @Operation(summary = "教材类型排序")
    @PostMapping("/type/sort")
    public BaseResult<Boolean> sortTextbookType(@RequestBody @Validated List<SortItemDTO> sortList) {
        return textbookService.sortTextbookType(sortList);
    }

    // ==================== 版本相关接口 ====================

    @Operation(summary = "获取版本列表")
    @GetMapping("/version/list")
    public BaseResult<List<PgTextbookVersion>> getTextbookVersionList(
            @Parameter(description = "教材类型ID") @RequestParam Long typeId) {
        return textbookService.getTextbookVersionList(typeId);
    }

    @Operation(summary = "创建版本")
    @PostMapping("/version")
    public BaseResult<IdResultVO> createTextbookVersion(@RequestBody @Validated PgTextbookVersionDTO dto) {
        return textbookService.createTextbookVersion(dto);
    }

    @Operation(summary = "更新版本")
    @PutMapping("/version/{id}")
    public BaseResult<Boolean> updateTextbookVersion(
            @Parameter(description = "版本ID") @PathVariable Long id,
            @RequestBody @Validated PgTextbookVersionDTO dto) {
        return textbookService.updateTextbookVersion(id, dto);
    }

    @Operation(summary = "删除版本")
    @DeleteMapping("/version/{id}")
    public BaseResult<Boolean> deleteTextbookVersion(
            @Parameter(description = "版本ID") @PathVariable Long id) {
        return textbookService.deleteTextbookVersion(id);
    }

    @Operation(summary = "版本排序")
    @PostMapping("/version/sort")
    public BaseResult<Boolean> sortTextbookVersion(@RequestBody @Validated List<SortItemDTO> sortList) {
        return textbookService.sortTextbookVersion(sortList);
    }

    // ==================== 年级相关接口 ====================

    @Operation(summary = "获取年级列表")
    @GetMapping("/grade/list")
    public BaseResult<List<PgTextbookGrade>> getTextbookGradeList(
            @Parameter(description = "版本ID") @RequestParam Long versionId) {
        return textbookService.getTextbookGradeList(versionId);
    }

    @Operation(summary = "创建年级")
    @PostMapping("/grade")
    public BaseResult<IdResultVO> createTextbookGrade(@RequestBody @Validated PgTextbookGradeDTO dto) {
        return textbookService.createTextbookGrade(dto);
    }

    @Operation(summary = "更新年级")
    @PutMapping("/grade/{id}")
    public BaseResult<Boolean> updateTextbookGrade(
            @Parameter(description = "年级ID") @PathVariable Long id,
            @RequestBody @Validated PgTextbookGradeDTO dto) {
        return textbookService.updateTextbookGrade(id, dto);
    }

    @Operation(summary = "删除年级")
    @DeleteMapping("/grade/{id}")
    public BaseResult<Boolean> deleteTextbookGrade(
            @Parameter(description = "年级ID") @PathVariable Long id) {
        return textbookService.deleteTextbookGrade(id);
    }

    @Operation(summary = "年级排序")
    @PostMapping("/grade/sort")
    public BaseResult<Boolean> sortTextbookGrade(@RequestBody @Validated List<SortItemDTO> sortList) {
        return textbookService.sortTextbookGrade(sortList);
    }


    // ==================== 册别相关接口 ====================

    @Operation(summary = "获取册别列表")
    @GetMapping("/volume/list")
    public BaseResult<List<PgTextbookVolume>> getTextbookVolumeList(
            @Parameter(description = "年级ID") @RequestParam Long gradeId) {
        return textbookService.getTextbookVolumeList(gradeId);
    }

    @Operation(summary = "创建册别")
    @PostMapping("/volume")
    public BaseResult<IdResultVO> createTextbookVolume(@RequestBody @Validated PgTextbookVolumeDTO dto) {
        return textbookService.createTextbookVolume(dto);
    }

    @Operation(summary = "更新册别")
    @PutMapping("/volume/{id}")
    public BaseResult<Boolean> updateTextbookVolume(
            @Parameter(description = "册别ID") @PathVariable Long id,
            @RequestBody @Validated PgTextbookVolumeDTO dto) {
        return textbookService.updateTextbookVolume(id, dto);
    }

    @Operation(summary = "删除册别")
    @DeleteMapping("/volume/{id}")
    public BaseResult<Boolean> deleteTextbookVolume(
            @Parameter(description = "册别ID") @PathVariable Long id) {
        return textbookService.deleteTextbookVolume(id);
    }

    @Operation(summary = "册别排序")
    @PostMapping("/volume/sort")
    public BaseResult<Boolean> sortTextbookVolume(@RequestBody @Validated List<SortItemDTO> sortList) {
        return textbookService.sortTextbookVolume(sortList);
    }

    // ==================== 单元相关接口 ====================

    @Operation(summary = "获取单元列表")
    @GetMapping("/unit/list")
    public BaseResult<List<PgTextbookUnit>> getTextbookUnitList(
            @Parameter(description = "册别ID") @RequestParam Long volumeId) {
        return textbookService.getTextbookUnitList(volumeId);
    }

    @Operation(summary = "创建单元")
    @PostMapping("/unit")
    public BaseResult<IdResultVO> createTextbookUnit(@RequestBody @Validated PgTextbookUnitDTO dto) {
        return textbookService.createTextbookUnit(dto);
    }

    @Operation(summary = "更新单元")
    @PutMapping("/unit/{id}")
    public BaseResult<Boolean> updateTextbookUnit(
            @Parameter(description = "单元ID") @PathVariable Long id,
            @RequestBody @Validated PgTextbookUnitDTO dto) {
        return textbookService.updateTextbookUnit(id, dto);
    }

    @Operation(summary = "删除单元")
    @DeleteMapping("/unit/{id}")
    public BaseResult<Boolean> deleteTextbookUnit(
            @Parameter(description = "单元ID") @PathVariable Long id) {
        return textbookService.deleteTextbookUnit(id);
    }

    @Operation(summary = "单元排序")
    @PostMapping("/unit/sort")
    public BaseResult<Boolean> sortTextbookUnit(@RequestBody @Validated List<SortItemDTO> sortList) {
        return textbookService.sortTextbookUnit(sortList);
    }
}
