package com.pgb.service.domain.zc.word.chinese;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.pgb.service.enums.GenerateStatusEnum;
import lombok.Data;

/**
 * 语文教材字词表
 * @TableName pg_chinese_word
 */
@TableName(value ="pg_zc_chinese_word")
@Data
public class PgZcChineseWord implements Serializable {
    /**
     *
     */
    private Long id;

    /**
     * 字词
     */
    private String word;

    /**
     * 拼音
     */
    private String pinyin;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 播报音频url
     */
    private String audioUrl;

    /**
     * 状态，单词信息生成的状态
     */
    private GenerateStatusEnum status;

}
