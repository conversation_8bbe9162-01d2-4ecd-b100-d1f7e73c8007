package com.pgb.service.domain.homework;

import java.io.Serializable;

import java.util.Date;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.enums.GradeEnum;
import com.pgb.service.enums.SubjectEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.Builder;

/**
 * 作业表
 *
 * @TableName pg_homework
 */
@Schema(description = "作业表 VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class PgHomeworkVO implements Serializable {

    @Schema(title = "作业id", type = "string")
    private Long id;

    @Schema(title = "创建人id")
    private Long creatorId;

    @Schema(title = "作业名称")
    private String name;

    @Schema(title = "作业题目信息")
    private Object questionInfo;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "更新时间")
    private Date updateTime;

    @Schema(title = "学生总数")
    private Integer totalNum;

    @Schema(title = "已提交人数")
    private Integer submitNum;

    @Schema(title = "未提交人数")
    private Integer unSubmitNum;

    @Schema(title = "Ai已批改数量")
    private Integer correctedNum;

    @Schema(title = "是否是作业创建者")
    private Boolean isCreator;

    @Schema(title = "所属班级")
    private String className;

    @Schema(title = "所属班级id")
    private Long classId;

    @Schema(title = "年级")
    private GradeEnum grade;

    @Schema(title = "班级")
    private String classNum;

    @Schema(title = "是否已作答")
    private Boolean isSubmit;

    @Schema(title = "作答id")
    private Long answerId;

    @Schema(title = "作答状态")
    private CorrectStatusEnum status;

    @Schema(title = "是否是审核模式")
    private Boolean isCheckMode;

    @Schema(title = "是否允许家长错篇重提")
    private Boolean isReSubmit;

    @Schema(title = "科目")
    private SubjectEnum subject;

    @Schema(title = "年级名称")
    @JsonGetter("gradeStr")
    public String getGradeStr() {
        return ObjectUtil.isNotNull(this.grade) ? this.grade.desc : null;
    }

}
