package com.pgb.service.custom.textbook;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.resource.InputStreamResource;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.core.global.GlobalCode;
import com.pgb.service.domain.zc.common.textbook.dto.*;
import com.pgb.service.domain.zc.common.textbook.entity.PgWordExample;
import com.pgb.service.domain.zc.common.textbook.entity.PgWordPronunciation;
import com.pgb.service.domain.zc.common.textbook.entity.PgWordTranslation;
import com.pgb.service.domain.zc.common.textbook.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * Created by 2025/7/28 16:39
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class WordService {


    @Value("${textbook.base-url}")
    private String baseUrl;

    /**
     * 发送POST请求
     */
    private String post(String url, Object body) {
        return HttpRequest.post(url)
                .body(JSONUtil.toJsonStr(body))
                .timeout(10 * 60 * 1000)
                .execute()
                .body();
    }

    /**
     * 发送PUT请求
     */
    private String put(String url, Object body) {
        return HttpRequest.put(url)
                .body(JSONUtil.toJsonStr(body))
                .timeout(10 * 60 * 1000)
                .execute()
                .body();
    }

    /**
     * 发送GET请求
     */
    private String get(String url) {
        return HttpRequest.get(url)
                .timeout(10 * 60 * 1000)
                .execute()
                .body();
    }

    /**
     * 发送DELETE请求
     */
    private String delete(String url) {
        return HttpRequest.delete(url)
                .timeout(10 * 60 * 1000)
                .execute()
                .body();
    }


    // ==================== 单词相关接口 ====================

    /**
     * 分页获取单词列表
     */
    public BaseResult<Page<WordPageVO>> getWordPage(WordPageDTO dto) {
        String result = this.post(
                StrUtil.format("{}/word/page", baseUrl),
                dto
        );

//        log.info("分页获取单词列表结果", result);

        BaseResult<Page<WordPageVO>> convert = Convert.convert(new TypeReference<BaseResult<Page<WordPageVO>>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 获取单词详情
     */
    public BaseResult<WordDetailVO> getWordDetail(Long id) {
        String result = this.get(
                StrUtil.format("{}/word/{}", baseUrl, id)
        );

//        log.info("获取单词详情结果：{}", result);

        BaseResult<WordDetailVO> convert = Convert.convert(new TypeReference<BaseResult<WordDetailVO>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 创建单词
     */
    public BaseResult<IdResultVO> createWord(PgWordDTO dto) {
        String result = this.post(
                StrUtil.format("{}/word", baseUrl),
                dto
        );

//        log.info("创建单词结果：{}", result);

        BaseResult<IdResultVO> convert = Convert.convert(new TypeReference<BaseResult<IdResultVO>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 更新单词
     */
    public BaseResult<Boolean> updateWord(Long id, WordUpdateDTO dto) {
        String result = this.put(
                StrUtil.format("{}/word/{}", baseUrl, id),
                dto
        );

//        log.info("更新单词结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 删除单词
     */
    public BaseResult<Boolean> deleteWord(Long id) {
        String result = this.delete(
                StrUtil.format("{}/word/{}", baseUrl, id)
        );

//        log.info("删除单词结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 批量录入单词
     */
    public BaseResult<BatchWordResultVO> batchCreateWords(BatchWordDTO dto) {
        String result = this.post(
                StrUtil.format("{}/word/batch", baseUrl),
                dto
        );

//        log.info("批量录入单词结果：{}", result);

        BaseResult<BatchWordResultVO> convert = Convert.convert(new TypeReference<BaseResult<BatchWordResultVO>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 单词排序
     */
    public BaseResult<Boolean> sortWords(List<SortItemDTO> sortList) {
        String result = this.post(
                StrUtil.format("{}/word/sort", baseUrl),
                sortList
        );

//        log.info("单词排序结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    // ==================== 单词翻译相关接口 ====================

    /**
     * 获取单词翻译列表
     */
    public BaseResult<List<PgWordTranslation>> getWordTranslationList(Long wordId) {
        String result = this.get(
                StrUtil.format("{}/word/translation/list?wordId={}", baseUrl, wordId)
        );

//        log.info("获取单词翻译列表结果：{}", result);

        BaseResult<List<PgWordTranslation>> convert = Convert.convert(new TypeReference<BaseResult<List<PgWordTranslation>>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 创建单词翻译
     */
    public BaseResult<IdResultVO> createWordTranslation(PgWordTranslationDTO dto) {
        String result = this.post(
                StrUtil.format("{}/word/translation", baseUrl),
                dto
        );

//        log.info("创建单词翻译结果：{}", result);

        BaseResult<IdResultVO> convert = Convert.convert(new TypeReference<BaseResult<IdResultVO>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 更新单词翻译
     */
    public BaseResult<Boolean> updateWordTranslation(Long id, PgWordTranslationDTO dto) {
        String result = this.put(
                StrUtil.format("{}/word/translation/{}", baseUrl, id),
                dto
        );

//        log.info("更新单词翻译结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 删除单词翻译
     */
    public BaseResult<Boolean> deleteWordTranslation(Long id) {
        String result = this.delete(
                StrUtil.format("{}/word/translation/{}", baseUrl, id)
        );

//        log.info("删除单词翻译结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    // ==================== 单词发音相关接口 ====================

    /**
     * 获取单词发音列表
     */
    public BaseResult<List<PgWordPronunciation>> getWordPronunciationList(Long wordId) {
        String result = this.get(
                StrUtil.format("{}/word/pronunciation/list?wordId={}", baseUrl, wordId)
        );

//        log.info("获取单词发音列表结果：{}", result);

        BaseResult<List<PgWordPronunciation>> convert = Convert.convert(new TypeReference<BaseResult<List<PgWordPronunciation>>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 创建单词发音
     */
    public BaseResult<IdResultVO> createWordPronunciation(PgWordPronunciationDTO dto) {
        String result = this.post(
                StrUtil.format("{}/word/pronunciation", baseUrl),
                dto
        );

//        log.info("创建单词发音结果：{}", result);

        BaseResult<IdResultVO> convert = Convert.convert(new TypeReference<BaseResult<IdResultVO>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 更新单词发音
     */
    public BaseResult<Boolean> updateWordPronunciation(Long id, PgWordPronunciation dto) {
        String result = this.put(
                StrUtil.format("{}/word/pronunciation/{}", baseUrl, id),
                dto
        );

//        log.info("更新单词发音结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 删除单词发音
     */
    public BaseResult<Boolean> deleteWordPronunciation(Long id) {
        String result = this.delete(
                StrUtil.format("{}/word/pronunciation/{}", baseUrl, id)
        );

//        log.info("删除单词发音结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    // ==================== 单词例句相关接口 ====================

    /**
     * 获取单词例句列表
     */
    public BaseResult<List<PgWordExample>> getWordExampleList(Long wordId) {
        String result = this.get(
                StrUtil.format("{}/word/example/list?wordId={}", baseUrl, wordId)
        );

//        log.info("获取单词例句列表结果：{}", result);

        BaseResult<List<PgWordExample>> convert = Convert.convert(new TypeReference<BaseResult<List<PgWordExample>>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 创建单词例句
     */
    public BaseResult<IdResultVO> createWordExample(PgWordExampleDTO dto) {
        String result = this.post(
                StrUtil.format("{}/word/example", baseUrl),
                dto
        );

//        log.info("创建单词例句结果：{}", result);

        BaseResult<IdResultVO> convert = Convert.convert(new TypeReference<BaseResult<IdResultVO>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 更新单词例句
     */
    public BaseResult<Boolean> updateWordExample(Long id, PgWordExampleDTO dto) {
        String result = this.put(
                StrUtil.format("{}/word/example/{}", baseUrl, id),
                dto
        );

//        log.info("更新单词例句结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    /**
     * 删除单词例句
     */
    public BaseResult<Boolean> deleteWordExample(Long id) {
        String result = this.delete(
                StrUtil.format("{}/word/example/{}", baseUrl, id)
        );

//        log.info("删除单词例句结果：{}", result);

        BaseResult<Boolean> convert = Convert.convert(new TypeReference<BaseResult<Boolean>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

    // ==================== 图片识别相关接口 ====================

    /**
     * 直接上传并识别图片中的英语单词
     */
    public BaseResult<DirectOcrResponseVO> directOcr(MultipartFile file, Long unitId) {
        log.info("直接上传并识别图片中的英语单词，文件名：{}，文件大小：{}，单元ID：{}",
                file.getOriginalFilename(), file.getSize(), unitId);

        try {
            // 验证文件
            if (file.isEmpty()) {
                return BaseResult.error("上传文件不能为空");
            }

            // 验证文件大小（10MB限制）
            if (file.getSize() > 10 * 1024 * 1024) {
                return BaseResult.error("文件大小不能超过10MB");
            }

            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return BaseResult.error("只支持图片文件格式");
            }

            // 使用InputStreamResource包装MultipartFile
            InputStreamResource resource = new InputStreamResource(
                    file.getInputStream(),
                    file.getOriginalFilename()
            );

            // 发送multipart/form-data请求
            HttpResponse response = HttpRequest.post(StrUtil.format("{}/word/image/direct-ocr", baseUrl))
                    .form("file", resource)
                    .form("unitId", unitId.toString())
                    .timeout(10 * 60 * 1000)
                    .execute();

            String result = response.body();

            BaseResult<DirectOcrResponseVO> convert = Convert.convert(
                    new TypeReference<BaseResult<DirectOcrResponseVO>>() {},
                    JSONUtil.parseObj(result)
            );

            if (convert.isSuccess()) {
                return convert;
            }

            return BaseResult.code(GlobalCode.Error);

        } catch (IOException e) {
            log.error("文件上传失败，文件名：{}，单元ID：{}", file.getOriginalFilename(), unitId, e);
            return BaseResult.error("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("图片识别失败，文件名：{}，单元ID：{}", file.getOriginalFilename(), unitId, e);
            return BaseResult.error("图片识别失败：" + e.getMessage());
        }
    }

    /**
     * 确认录入识别的单词
     */
    public BaseResult<BatchWordResultVO> confirmWords(ImageWordConfirmDTO dto) {
        String result = this.post(
                StrUtil.format("{}/word/image/confirm", baseUrl),
                dto
        );

//        log.info("确认录入识别的单词结果：{}", result);

        BaseResult<BatchWordResultVO> convert = Convert.convert(new TypeReference<BaseResult<BatchWordResultVO>>() {
        }, JSONUtil.parseObj(result));

        if (convert.isSuccess()) {
            return convert;
        }

        return BaseResult.code(GlobalCode.Error);
    }

}
