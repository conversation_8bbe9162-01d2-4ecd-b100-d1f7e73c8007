package com.pgb.service.db.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.ai.LLMService;
import com.pgb.ai.domain.GPTAnswer;
import com.pgb.common.core.global.BaseResult;
import com.pgb.common.oss.service.OssService;
import com.pgb.service.domain.zc.word.english.EngWordItem;
import com.pgb.service.domain.zc.word.english.common.PgZcEngWordCommon;
import com.pgb.service.db.PgZcEngWordCommonService;
import com.pgb.service.enums.GenerateStatusEnum;
import com.pgb.service.factory.LLMServiceFactory;
import com.pgb.service.mapper.PgZcEngWordCommonMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @description 针对表【pg_zc_eng_word_common】的数据库操作Service实现
 * @createDate 2025-06-06 18:42:15
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PgZcEngWordCommonServiceImpl extends ServiceImpl<PgZcEngWordCommonMapper, PgZcEngWordCommon>
        implements PgZcEngWordCommonService {

    private final OssService ossService;

    private final LLMServiceFactory llmServiceFactory;

    private LLMService getLLMService() {
        return llmServiceFactory.getLLMService("ali");
    }

    @Override
    public void getEnWordAudio(PgZcEngWordCommon enWord) {

        // 生成英式发音
        String ukAudioUrl = generateAndUploadAudio(enWord.getWord(), 1);
        if (StrUtil.isNotBlank(ukAudioUrl)) {
            enWord.setUkAudioUrl(ukAudioUrl);
        } else {
            throw new RuntimeException("获取英式发音失败");
        }

        // 生成美式发音
        String usAudioUrl = generateAndUploadAudio(enWord.getWord(), 2);
        if (StrUtil.isNotBlank(usAudioUrl)) {
            enWord.setUsAudioUrl(usAudioUrl);
        } else {
            throw new RuntimeException("获取美式发音失败");
        }

        // 更新数据库
        updateById(enWord);
    }

    // 生成并上传音频
    private String generateAndUploadAudio(String enWord, Integer type) {

        try {
            String url = StrUtil.format("http://dict.youdao.com/dictvoice?audio={}&type={}", enWord, type);
            String suffix = type == 1 ? "_1.mp3" : "_2.mp3";

            // 先检查响应状态码
            int status = HttpRequest.get(url)
                    .timeout(20 * 60 * 1000)
                    .execute()
                    .getStatus();
            // 跳过
            if (!(status == 200)) {
                return null;
            }

            byte[] result = HttpRequest.get(url)
                    .timeout(20 * 60 * 1000)
                    .execute()
                    .bodyBytes();
            enWord = ReUtil.delAll("[^a-zA-Z' -()]", enWord);

            String enWordStr = URLEncoder.encode(enWord, StandardCharsets.UTF_8);

            String key = StrUtil.format("resource/eng/audio/{}/{}{}", enWordStr, enWordStr, suffix);

            File tempFile = FileUtil.createTempFile(".mp3", true);
            FileUtil.writeBytes(result, tempFile);
            String audioUrl = ossService.putFile(key, tempFile);
            FileUtil.del(tempFile);

            log.info("获取单词{}发音：{}：{}", type == 1 ? "英式" : "美式", enWord, audioUrl);
            return audioUrl;
        } catch (Exception e) {
            log.error("获取单词发音失败：{} - {}", enWord, e.getMessage());
            return null;
        }
    }

    public static void main(String[] args) {

        String enWord = "become";
        Integer type = 1;
        String url = StrUtil.format("http://dict.youdao.com/dictvoice?audio={}&type={}", enWord, type);

        // 先检查响应状态码
        int status = HttpRequest.get(url)
                .timeout(20 * 60 * 1000)
                .execute()
                .getStatus();

        String body = HttpRequest.get(url)
                .timeout(20 * 60 * 1000)
                .execute()
                .body();
        System.out.println("结果" + status);

    }

    @Override
    public PgZcEngWordCommon getEnWordInfo(PgZcEngWordCommon word) {

        // 调用LLM接口获取单词信息
        String prompt = """
                你是一位资深的英语翻译教师，对提供的英语单词或词组，给出音标和中文释义。
                涉及的释义，仅限从小学到大学阶段，不需要偏僻的释义。
                
                【输出格式要求】
                请严格按照以下 json 格式进行返回：
                {
                "word": "英语单词或词组",
                "phonetic": "音标",
                "chinese": "中文释义",
                "wordClass": "词性，简写版"
                }
                """;

        try {
            LLMService llmService = getLLMService();
            GPTAnswer answer = llmService.chatComplete(prompt, List.of(word.getWord()), true, null);

            if (ObjectUtil.isNull(answer) || StrUtil.isBlank(answer.getAnswer())) {
                throw new RuntimeException("LLM返回结果为空");
            }

            JSONObject json = JSONUtil.parseObj(answer.getAnswer());
            EngWordItem wordItem = JSONUtil.toBean(json, EngWordItem.class);

            // 4. 处理不同情况
            if (ObjectUtil.isNotNull(word)) {
                // 存在但信息不全，则更新
                if (StrUtil.isBlank(word.getPhonetic())) {
                    word.setPhonetic(wordItem.getPhonetic());
                }
                if (StrUtil.isBlank(word.getChinese())) {
                    word.setChinese(wordItem.getChinese());
                }
                if (StrUtil.isBlank(word.getWordClass())) {
                    word.setWordClass(wordItem.getWordClass());
                }
                // 更新
                updateById(word);
                return word;
            } else {
                // 不存在则创建新记录
                word = new PgZcEngWordCommon();
                word.setWord(wordItem.getWord());
                word.setPhonetic(wordItem.getPhonetic());
                word.setChinese(wordItem.getChinese());
                word.setWordClass(wordItem.getWordClass());
                save(word);
                return word;
            }
        } catch (Exception e) {
            log.error("获取单词信息失败，单词：{}，错误：{}", word, e.getMessage(), e);
            throw new RuntimeException("获取单词音标和释义失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void processWordsAsync(String text) {

        // 换行分割 保存
        List<String> words = Arrays.stream(text.split("\\n")).toList();

        words.stream()
//                .map(item -> ReUtil.delAll("[^a-zA-Z' -()]", item))
                .map(String::trim)
                .filter(StrUtil::isNotBlank) // 过滤空值
                .forEach(word -> {
                    // 先保存单词基本信息，设置初始状态
                    PgZcEngWordCommon engWord = getOne(new LambdaQueryWrapper<PgZcEngWordCommon>()
                            .eq(PgZcEngWordCommon::getWord, word)
                            .last("limit 1")
                    );
                    if (ObjectUtil.isNull(engWord)) {
                        engWord = new PgZcEngWordCommon();
                        engWord.setWord(word);
                        engWord.setStatus(GenerateStatusEnum.Init);
                        save(engWord);
                    }

                    // 判断单词生成状态
                    if (engWord.getStatus() == GenerateStatusEnum.Completed) {
                        log.info("单词已生成完毕：{}", word);
                        return;
                    }

                    // 执行生成音标释义发音的内容
                    PgZcEngWordCommon finalEngWord = engWord;
                    CompletableFuture.runAsync(() -> {

                        log.info("开始生成单词信息：{}", finalEngWord.getWord());
                        try {
                            // 更新状态为生成中
                            update(new LambdaUpdateWrapper<PgZcEngWordCommon>()
                                    .eq(PgZcEngWordCommon::getId, finalEngWord.getId())
                                    .set(PgZcEngWordCommon::getStatus, GenerateStatusEnum.Generating)
                            );

                            // 获取音标和释义
                            getEnWordInfo(finalEngWord);
                            // 获取单词发音
                            getEnWordAudio(finalEngWord);

                            // 更新状态为完成
                            update(new LambdaUpdateWrapper<PgZcEngWordCommon>()
                                    .eq(PgZcEngWordCommon::getId, finalEngWord.getId())
                                    .set(PgZcEngWordCommon::getStatus, GenerateStatusEnum.Completed));

                            log.info("单词信息生成完成：{}", finalEngWord.getWord());
                        } catch (Exception e) {
                            log.error("生成单词信息失败，单词：{}，错误：{}", finalEngWord.getWord(), e.getMessage(), e);
                            // 设置失败状态
                            update(new LambdaUpdateWrapper<PgZcEngWordCommon>()
                                    .eq(PgZcEngWordCommon::getId, finalEngWord.getId())
                                    .set(PgZcEngWordCommon::getStatus, GenerateStatusEnum.Failed));
                        }
                    });
                });
    }

}




