package com.pgb.service.db.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pgb.service.domain.homework.PgHomework;
import com.pgb.service.db.PgHomeworkService;
import com.pgb.service.domain.question.PgQuestionDTO;
import com.pgb.service.enums.GradeEnum;
import com.pgb.service.enums.SubjectEnum;
import com.pgb.service.enums.WritingStyleEnum;
import com.pgb.service.mapper.PgHomeworkMapper;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【pg_homework(作业表)】的数据库操作Service实现
* @createDate 2024-12-10 19:36:38
*/
@Service
public class PgHomeworkServiceImpl extends ServiceImpl<PgHomeworkMapper, PgHomework>
    implements PgHomeworkService{

    @Override
    public void createSampleHomework(Long userId, Long classId) {

        PgHomework homework = new PgHomework();
        homework.setCreatorId(userId);
        homework.setName("示例作业");

        // 默认题目
        PgQuestionDTO question = new PgQuestionDTO();
        question.setName("默认");
        question.setCorrectRequest("默认");
        question.setWritingRequest("默认");
        question.setGrade(GradeEnum.GRADE_3);
        question.setSubject(SubjectEnum.Chinese);
        question.setWordNum(300);
        question.setStyle(WritingStyleEnum.Other);
        question.setScore(30);
        homework.setQuestionInfo(question);

        homework.setCreateTime(new Date());
        homework.setClassId(classId);
        save(homework);

    }
}




